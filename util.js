export function parseStreamData(rawString) {
  const lines = rawString.split('\n');
  let result = '';

  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const content = line.slice(6).trim(); // 移除 'data: ' 前缀并去除首尾空白
      if (content && content !== '[DONE]') {
        result += content;
      }
    }
  }

  return result;
}

export function parseDataUrls(rawString) {
  const lines = rawString.split('\n');
  let dataUrls = [];

  for (const line of lines) {
    if (line.startsWith('dataUrl: ')) {
      const content = line.slice(8).trim(); // 移除 'dataUrl: ' 前缀并去除首尾空白
      if (content) {
        dataUrls.push(content);
      }
    }
  }

  return dataUrls;
}

export function arrayBufferToString(arrayBuffer) {
  // 将 ArrayBuffer 转换为字符串
  const uint8Array = new Uint8Array(arrayBuffer);
  let string = '';
  for (let i = 0; i < uint8Array.length; i++) {
    string += String.fromCharCode(uint8Array[i]);
  }
  return decodeURIComponent(escape(string));
}

export function uint8ArrayToString(uint8Arr) {
  let out = "";
  let i = 0;
  const len = uint8Arr.length;

  while (i < len) {
    let char1 = uint8Arr[i++];
    if (char1 < 0x80) {
      out += String.fromCharCode(char1);
    } else if (char1 < 0xE0) {
      let char2 = uint8Arr[i++];
      out += String.fromCharCode(((char1 & 0x1F) << 6) | (char2 & 0x3F));
    } else if (char1 < 0xF0) {
      let char2 = uint8Arr[i++];
      let char3 = uint8Arr[i++];
      out += String.fromCharCode(((char1 & 0x0F) << 12) | ((char2 & 0x3F) << 6) | (char3 & 0x3F));
    } else {
      let char2 = uint8Arr[i++];
      let char3 = uint8Arr[i++];
      let char4 = uint8Arr[i++];
      let codepoint = (((char1 & 0x07) << 18) | ((char2 & 0x3F) << 12) | ((char3 & 0x3F) << 6) | (char4 & 0x3F)) - 0x10000;
      out += String.fromCharCode((codepoint >> 10) + 0xD800, (codepoint & 0x3FF) + 0xDC00);
    }
  }

  return out;
}

const sysInfo = wx.getSystemInfoSync();
export const isDevTool = sysInfo.platform === "devtools";

export function formatTimeFromTimestamp(timestamp, type = 0) {
  const date = new Date(timestamp);
  // 将时间戳转换为秒
  let seconds = Math.floor(timestamp / 1000);
  // 计算天数、小时数和分钟数
  let days = Math.floor(seconds / (24 * 60 * 60));
  seconds %= 24 * 60 * 60;
  let hours = Math.floor(seconds / (60 * 60));
  seconds %= 60 * 60;
  let minutes = Math.floor(seconds / 60);
  seconds %= 60;

  let year, month;

  // 构建格式化字符串
  let result = "";
  if (type === 0) {
    // 用于时间戳计算时间
    result += days + "天";
    result += hours + "小时";
    result += minutes + "分";
  } else {
    // 用于格式化时间戳YYYY-MM-DD HH-mm-ss
    year = date.getFullYear();
    month = (date.getMonth() + 1).toString().padStart(2, "0");
    days = date.getDate().toString().padStart(2, "0");
    hours = date.getHours().toString().padStart(2, "0");
    minutes = date.getMinutes().toString().padStart(2, "0");
    seconds = date.getSeconds().toString().padStart(2, "0");
    if (type === 1) {
      // 此处用来返回YYYY-MM-DD HH-mm-ss
      result = `${year}-${month}-${days} ${hours}:${minutes}:${seconds}`;
    } else if (type === 2) {
      // 此处用来返回YYYY-MM-DD
      result = `${year}-${month}-${days}`;
    } else if (type === 3) {
      // 此处用来返回YYYY-MM
      result = `${year}-${month}`;
    } else if (type === 4) {
      // 此处用来返回 HH-mm-ss
      result = `${hours}:${minutes}:${seconds}`;
    } else if (type === 5) {
      // 此处用来返回HH-mm
      result = `${hours}:${minutes}`;
    } else if (type === 6) {
      // 此处用来返回 MM-DD HH-mm-ss
      result = `${month}-${days} ${hours}:${minutes}:${seconds}`;
    } else if (type === 7) {
      // 此处用来返回 YYYY.MM.DD HH:mm:ss
      result = `${year}-${month}-${days} ${hours}:${minutes}`;
    }
  }

  return result;
}

export function image2Base64(imgUrl) {
  wx.chooseImage({
    count: 1, // 默认9 
    sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有 
    sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有 
    success(res) {
      wx.getFileSystemManager().readFile({
        filePath: res.tempFilePaths[0], //要读取的文件的路径 (本地路径)
        encoding: "base64", //指定读取文件的字符编码，如果不传 encoding，则以 ArrayBuffer 格式读取文件的二进制内容
        success(res) {
          //转换完毕，执行上传
          _this.setData({
            avatar: 'data:image/png;base64,' + res.data
          })
        }
      })
    }
  })
}