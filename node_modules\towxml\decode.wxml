<block wx:for="{{nodes.child}}" wx:for-index="i" wx:for-item="item" wx:key="i"><block wx:if="{{item.tag===undefined}}">{{item.text}}</block><view wx:if="{{item.tag==='view'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap"><decode wx:if="{{item.child}}" nodes="{{item}}"/></view><video wx:if="{{item.tag==='video'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap" poster="{{item.attr.poster}}" src="{{item.attr.src}}"><decode wx:if="{{item.child}}" nodes="{{item}}"/></video><text wx:if="{{item.tag==='text'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap"><decode wx:if="{{item.child}}" nodes="{{item}}"/></text><image wx:if="{{item.tag==='image'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap" src="{{item.attr.src}}" mode="{{item.attr.mode ? item.attr.mode : 'widthFix'}}" lazy-load="{{item.attr['lazy-load']}}"><decode wx:if="{{item.child}}" nodes="{{item}}"/></image><navigator wx:if="{{item.tag==='navigator'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap" url="{{item.attr.href}}"><decode wx:if="{{item.child}}" nodes="{{item}}"/></navigator><swiper wx:if="{{item.tag==='swiper'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap"><decode wx:if="{{item.child}}" nodes="{{item}}"/></swiper><swiper-item wx:if="{{item.tag==='swiper-item'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap"><decode wx:if="{{item.child}}" nodes="{{item}}"/></swiper-item><block wx:if="{{item.tag==='block'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap"><decode wx:if="{{item.child}}" nodes="{{item}}"/></block><form wx:if="{{item.tag==='form'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap"><decode wx:if="{{item.child}}" nodes="{{item}}"/></form><input wx:if="{{item.tag==='input'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap"><decode wx:if="{{item.child}}" nodes="{{item}}"/></input><textarea wx:if="{{item.tag==='textarea'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap"><decode wx:if="{{item.child}}" nodes="{{item}}"/></textarea><button wx:if="{{item.tag==='button'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap"><decode wx:if="{{item.child}}" nodes="{{item}}"/></button><checkbox-group wx:if="{{item.tag==='checkbox-group'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap" catch:change="_change" bindchange="{{item.attr.bindchange}}"><decode wx:if="{{item.child}}" nodes="{{item}}"/></checkbox-group><checkbox wx:if="{{item.tag==='checkbox'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap" checked="{{item.attr.checked}}" value="{{item.attr.value}}"><decode wx:if="{{item.child}}" nodes="{{item}}"/></checkbox><radio-group wx:if="{{item.tag==='radio-group'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap"><decode wx:if="{{item.child}}" nodes="{{item}}"/></radio-group><radio wx:if="{{item.tag==='radio'}}" data-data="{{item}}" class="{{item.attr.class}}" data="{{item.attr.data}}" id="{{item.attr.id}}" style="{{item.attr.style}}" catch:tap="_tap" checked="{{item.attr.checked}}"><decode wx:if="{{item.child}}" nodes="{{item}}"/></radio><block wx:if="{{item.tag==='audio-player'}}"><audio-player data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='latex'}}"><latex data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='table'}}"><table data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='todogroup'}}"><todogroup data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='yuml'}}"><yuml data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='img'}}"><img data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block></block>