// pages/imageChater/imageChater.js
import Audio from '../../audio'
const fs = wx.getFileSystemManager()
const app = getApp()

// 尝试加载towxml，如果失败则使用降级方案
let towxml = null
try {
  towxml = require('towxml')
} catch (error) {
  console.warn('towxml加载失败，将使用普通文本显示:', error)
}
Page({

  data: {
    imageUrl: '',
    titleText: '',
    markdownData: null,
    isStreaming: false,
    recogType: 0
  },


  onLoad(options) {
    this.Audio = new Audio()

    if (options.imageUrl) {
      this.setData({
        imageUrl: options.imageUrl
      })
    }
  },

  onReady() {
    this.recognizeImage()
  },


  onUnload() {
    this.Audio.stop()
  },

  onTapTest(e) {
    this.startRegconize();
  },

  // 简单的markdown格式化（降级方案）
  formatMarkdownText(markdownText) {
    if (!markdownText) return '';

    let formatted = markdownText
      // 处理标题
      .replace(/^### (.*$)/gim, '📌 $1')
      .replace(/^## (.*$)/gim, '🔸 $1')
      .replace(/^# (.*$)/gim, '📋 $1')
      // 处理粗体
      .replace(/\*\*(.*?)\*\*/g, '$1')
      // 处理斜体
      .replace(/\*(.*?)\*/g, '$1')
      // 处理行内代码
      .replace(/`(.*?)`/g, '「$1」')
      // 处理LaTeX公式（简单替换）
      .replace(/\\\((.*?)\\\)/g, '[$1]')
      .replace(/\$\$(.*?)\$\$/g, '\n[$1]\n')
      // 处理列表
      .replace(/^- (.*$)/gim, '• $1')
      .replace(/^\* (.*$)/gim, '• $1')
      // 处理换行
      .replace(/\n\n/g, '\n\n');

    return formatted;
  },

  // 将markdown文本转换为towxml数据
  convertMarkdownToTowxml(markdownText) {
    // 如果towxml没有加载成功，返回null使用降级方案
    if (!towxml) {
      return null;
    }

    try {
      const result = towxml(markdownText, 'markdown', {
        base: '', // 相对路径的基础路径
        theme: 'light', // 主题，可选值：light、dark
        events: {
          // 为元素绑定事件
        }
      });
      return result;
    } catch (error) {
      console.error('Markdown转换失败:', error);
      return null;
    }
  },

  recognizeImage() {
    if (this.data.imageUrl) {
      wx.getFileSystemManager().readFile({
        filePath: this.data.imageUrl, //要读取的文件的路径 (本地路径)
        encoding: "base64", //指定读取文件的字符编码，如果不传 encoding，则以 ArrayBuffer 格式读取文件的二进制内容
        success: res => {
          this.isAlreadyInit = false
          this.startChat(`
      
# 你将分析题目信息，列出题目内容：
- 如果涉及到计算或公式请用latex格式表示，如\(x^2 - x + 1\)
- 如果题目涉及图片，请详细描述图片信息，包括图片中数字，物体，细节等，请尽可能详细地描述所有对于解题有用等细节，不要遗漏任何重要信息。
- 回答需要按照以下格式：
    - 题干：详细题目内容，包括选项，题目图片等的详细描述

        `, ['data:image/jpg;base64,' + res.data])
        }
      })
    }
  },

  startChat(text = "", imageUrls = []) {
    this.Audio.stop()
    this.socketTask = wx.connectSocket({
      url: 'wss://yuuki.my/ws2/',
      success: res => {
        console.log(res)
      }
    })

    let index = 0
    let isPlay = false

    this.socketTask.onOpen(() => {
      this.socketTask.send({
        data: JSON.stringify({
          asrText: text,
          imageUrls: imageUrls
        })
      })
      this.setData({
        isStreaming: true,
        titleText: ''
      })

    })

    this.socketTask.onMessage(async message => {
      const response = JSON.parse(message.data);
      console.log(response)
      const assistantMessage = {
        role: 'assistant',
        content: this.data.titleText,
        voiceUrl: '',
        audioList: []
      }
      const {
        finish_reason,
        target_text,
        target_audio,
        image_url
      } = response
      if (finish_reason === 'STOP') {
        console.log(new Date().toISOString(), 'finish');
        this.Audio.endSend = true
        this.Audio.audioLength = index

        if (!this.isAlreadyInit) {
          this.isAlreadyInit = true
          this.recognizedText = this.data.titleText
        }

        const allText = this.data.titleText
        // 找到所有的###，在###前面加\n\n
        const newText = allText.replace(/###/g, '\n\n###')

        // 转换为towxml格式
        const markdownData = this.convertMarkdownToTowxml(newText)
        // 如果towxml不可用，使用格式化文本
        const formattedText = markdownData ? newText : this.formatMarkdownText(newText)

        this.socketTask.close()
        this.setData({
          isStreaming: false,
          titleText: formattedText,
          markdownData: markdownData
        }, () => {});
      } else {
        assistantMessage.content += target_text;
        // 实时转换markdown
        const markdownData = this.convertMarkdownToTowxml(assistantMessage.content)
        // 如果towxml不可用，使用格式化文本
        const displayText = markdownData ? assistantMessage.content : this.formatMarkdownText(assistantMessage.content)
        this.setData({
          titleText: displayText,
          markdownData: markdownData
        })
        // if (image_url) {
        //     const path = await this.base64ToJpg(image_url)
        //     assistantMessage.imageSrc = path
        // }
        if (image_url) assistantMessage.imageSrc = image_url


        const audio = target_audio
        if (!audio) return;
        let filePath = ''
        if (audio.includes('http')) {
          filePath = target_audio
          this.Audio.audioList.push({
            filePath,
            index
          })
          assistantMessage.audioList.push({
            filePath,
            index
          })
          if (!isPlay) {
            this.Audio.play()
            isPlay = true
            console.log('call play')
          }
        } else {
          const audioArrayBuffer = wx.base64ToArrayBuffer(audio);
          index++
          filePath = `${wx.env.USER_DATA_PATH}/audio_${new Date().valueOf()}.mp3`; // 定义临时文件路径
          fs.writeFile({
            filePath: filePath,
            data: audioArrayBuffer,
            encoding: "binary",
            success: () => {
              console.log("Audio file saved successfully.");

              this.Audio.audioList.push({
                filePath,
                index
              })
              assistantMessage.audioList.push({
                filePath,
                index
              })

              if (!isPlay) {
                this.Audio.play()
                isPlay = true
                console.log('call play')
              }
            },
            fail: (error) => {
              console.error("Failed to save audio file:", error);
            },
          });
        }
      }

    })
  },

  shot() {
    this.setData({
      recogType: 0
    })
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sizeType: 'compressed',
      success: res => {
        let temp = res.tempFiles[0].tempFilePath
        wx.cropImage({
          src: temp,
          cropScale: "1:1",
          success: async r => {
            let tempCrop = r.tempFilePath

            this.setData({
              imageUrl: tempCrop
            }, () => {
              this.recognizeImage()
            })
          },
          fail: err => {
            console.log(err)
            wx.showToast({
              title: '拍照失败',
              icon: 'error'
            })
          }
        })
      },
      fail: err => {
        console.log(err)
      }
    })
  },



})