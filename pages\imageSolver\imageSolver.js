// pages/imageChater/imageChater.js
import Audio from '../../audio'
const fs = wx.getFileSystemManager()
const app = getApp()
Page({

  data: {
    imageUrl: '',
    titleText: '',
    isStreaming: false,
    recogType: 0
  },


  onLoad(options) {
    this.Audio = new Audio()

    if (options.imageUrl) {
      this.setData({
        imageUrl: options.imageUrl
      })
    }
  },

  onReady() {
    this.recognizeImage()
  },


  onUnload() {
    this.Audio.stop()
  },

  onTapTest(e) {
    this.startRegconize();
  },

  recognizeImage() {
    if (this.data.imageUrl) {
      wx.getFileSystemManager().readFile({
        filePath: this.data.imageUrl, //要读取的文件的路径 (本地路径)
        encoding: "base64", //指定读取文件的字符编码，如果不传 encoding，则以 ArrayBuffer 格式读取文件的二进制内容
        success: res => {
          this.isAlreadyInit = false
          this.startChat(`
      
# 你将分析题目信息，列出题目内容：
- 如果涉及到计算或公式请用latex格式表示，如\(x^2 - x + 1\)
- 如果题目涉及图片，请详细描述图片信息，包括图片中数字，物体，细节等，请尽可能详细地描述所有对于解题有用等细节，不要遗漏任何重要信息。
- 回答需要按照以下格式：
    - 题干：详细题目内容，包括选项，题目图片等的详细描述

        `, ['data:image/jpg;base64,' + res.data])
        }
      })
    }
  },

  startChat(text = "", imageUrls = []) {
    this.Audio.stop()
    this.socketTask = wx.connectSocket({
      url: 'wss://yuuki.my/ws2/',
      success: res => {
        console.log(res)
      }
    })

    let index = 0
    let isPlay = false

    this.socketTask.onOpen(() => {
      this.socketTask.send({
        data: JSON.stringify({
          asrText: text,
          imageUrls: imageUrls
        })
      })
      this.setData({
        isStreaming: true,
        titleText: ''
      })

    })

    this.socketTask.onMessage(async message => {
      const response = JSON.parse(message.data);
      console.log(response)
      const assistantMessage = {
        role: 'assistant',
        content: this.data.titleText,
        voiceUrl: '',
        audioList: []
      }
      const {
        finish_reason,
        target_text,
        target_audio,
        image_url
      } = response
      if (finish_reason === 'STOP') {
        console.log(new Date().toISOString(), 'finish');
        this.Audio.endSend = true
        this.Audio.audioLength = index

        if (!this.isAlreadyInit) {
          this.isAlreadyInit = true
          this.recognizedText = this.data.titleText
        }

        const allText = this.data.titleText
        // 找到所有的###，在###前面加\n\n
        const newText = allText.replace(/###/g, '\n\n###')

        this.socketTask.close()
        this.setData({
          isStreaming: false,
          titleText: newText
        }, () => {});
      } else {
        assistantMessage.content += target_text;
        this.setData({
          titleText: assistantMessage.content
        })
        // if (image_url) {
        //     const path = await this.base64ToJpg(image_url)
        //     assistantMessage.imageSrc = path
        // }
        if (image_url) assistantMessage.imageSrc = image_url


        const audio = target_audio
        if (!audio) return;
        let filePath = ''
        if (audio.includes('http')) {
          filePath = target_audio
          this.Audio.audioList.push({
            filePath,
            index
          })
          assistantMessage.audioList.push({
            filePath,
            index
          })
          if (!isPlay) {
            this.Audio.play()
            isPlay = true
            console.log('call play')
          }
        } else {
          const audioArrayBuffer = wx.base64ToArrayBuffer(audio);
          index++
          filePath = `${wx.env.USER_DATA_PATH}/audio_${new Date().valueOf()}.mp3`; // 定义临时文件路径
          fs.writeFile({
            filePath: filePath,
            data: audioArrayBuffer,
            encoding: "binary",
            success: () => {
              console.log("Audio file saved successfully.");

              this.Audio.audioList.push({
                filePath,
                index
              })
              assistantMessage.audioList.push({
                filePath,
                index
              })

              if (!isPlay) {
                this.Audio.play()
                isPlay = true
                console.log('call play')
              }
            },
            fail: (error) => {
              console.error("Failed to save audio file:", error);
            },
          });
        }
      }

    })
  },

  shot() {
    this.setData({
      recogType: 0
    })
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sizeType: 'compressed',
      success: res => {
        let temp = res.tempFiles[0].tempFilePath
        wx.cropImage({
          src: temp,
          cropScale: "1:1",
          success: async r => {
            let tempCrop = r.tempFilePath

            this.setData({
              imageUrl: tempCrop
            }, () => {
              this.recognizeImage()
            })
          },
          fail: err => {
            console.log(err)
            wx.showToast({
              title: '拍照失败',
              icon: 'error'
            })
          }
        })
      },
      fail: err => {
        console.log(err)
      }
    })
  },



})