// components/status_bar/bar.js
const app = getApp();
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        title: {
            type: String,
            value: '标题'
        },
        subTitle: {
            type: String,
            value: ''
        },
        backSvgStyle: {
            type: String,
            value: ''
        },
        titleStyle: {
            type: String,
            value: ''
        },
        isShowBack: {
            type: Boolean,
            value: true
        },
        background: {
            type: String,
            value: 'transparent'
        },
        isCustomBack: {
            type: Boolean,
            value: false
        },
        isCustomBar: {
            type: Boolean,
            value: false
        },
        isShowWXStatusBar: {
            type: Boolean,
            value: true
        },
    },

    /**
     * 组件的初始数据
     */
    data: {
        statusBarHeight: app.globalData.statusHeight,
        navigationBarHeight: app.globalData.navHeight,
    },

    /**
     * 组件的方法列表
     */
    methods: {
        bindtapBack(e) {
            if (this.properties.isCustomBack) {
                this.triggerEvent('back')
            } else {
                wx.navigateBack()
            }
        },
    },
});