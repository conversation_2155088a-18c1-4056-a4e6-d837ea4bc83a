<!--components/status_bar/bar.wxml-->

<!-- 页面属性 -->
<!-- background, title, isShowBack, backSvgStyle, titleStyle, isCustomBar, subTitle -->
<!-- 如果给了isCustomBack是true，则点击后会回调back -->

<!-- 导航栏 -->
<view class="navigation-bar" style="height:{{navigationBarHeight}}rpx;background:{{background}}">
</view>
<!-- 微信状态栏 -->
<view wx:if="{{isShowWXStatusBar}}" class="status-bar" style="height:{{statusBarHeight}}rpx;background: {{background}};">
    <!-- isCustomBar开启则代表该行自定义 -->
    <block wx:if="{{isCustomBar}}">
        <slot></slot>
    </block>
    <block wx:else>
        <!-- 返回图标 -->
        <view class="back" wx:if="{{isShowBack}}" bind:tap="bindtapBack">
            <view class="svg back-svg {{backSvgStyle}}"></view>
        </view>
        <!-- 标题 -->
        <view wx:if="{{isShowBack}}" class="title {{titleStyle}}">{{title}}
            <!-- 副标题 -->
            <view wx:if="{{subTitle}}" class="sub-title {{titleStyle}}">{{subTitle}}</view>
        </view>
        <!-- 没有返回按钮时的标题 -->
        <text wx:else class="title {{titleStyle}} title-left">{{title}}</text>

    </block>

</view>