/* pages/imageChater/imageChater.wxss */
@import '/common.wxss';

.page {
  background-color: #f5f5f5;
  height: 100vh;
}

.content {
  padding: 28rpx 32rpx;
  overflow: auto;
  max-height: 82vh;
}

.image-box {
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.loading-image {
  width: 80rpx;
  height: 80rpx;
}

.operations {
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.green {
  background: linear-gradient(90deg, #c1e445, #b5d442 100%);
}

.gray {
  background: linear-gradient(90deg, #6f457c, #54355e 100%);
}