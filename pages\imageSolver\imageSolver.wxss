/* pages/imageChater/imageChater.wxss */
@import '/common.wxss';

.page {
  background-color: #f5f5f5;
  height: 100vh;
}

.content {
  padding: 28rpx 32rpx;
  overflow: auto;
  max-height: 82vh;
}

.image-box {
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.loading-image {
  width: 80rpx;
  height: 80rpx;
}

.operations {
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.green {
  background: linear-gradient(90deg, #c1e445, #b5d442 100%);
}

.gray {
  background: linear-gradient(90deg, #6f457c, #54355e 100%);
}

/* Markdown内容样式 */
.title-text {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* towxml组件样式覆盖 */
.title-text towxml {
  font-size: 28rpx;
  line-height: 1.6;
}

/* 数学公式样式 */
.title-text .katex {
  font-size: 32rpx !important;
}

/* 标题样式 */
.title-text .h1,
.title-text .h2,
.title-text .h3 {
  color: #333;
  font-weight: bold;
  margin: 20rpx 0 16rpx 0;
}

.title-text .h1 {
  font-size: 36rpx;
}

.title-text .h2 {
  font-size: 32rpx;
}

.title-text .h3 {
  font-size: 30rpx;
}

/* 段落样式 */
.title-text .p {
  margin: 16rpx 0;
  color: #666;
}

/* 列表样式 */
.title-text .ul,
.title-text .ol {
  margin: 16rpx 0;
  padding-left: 32rpx;
}

.title-text .li {
  margin: 8rpx 0;
  color: #666;
}

/* 代码样式 */
.title-text .code {
  background: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-family: 'Courier New', monospace;
  font-size: 26rpx;
}

.title-text .pre {
  background: #f5f5f5;
  padding: 20rpx;
  border-radius: 12rpx;
  margin: 16rpx 0;
  overflow-x: auto;
}

/* 强调样式 */
.title-text .strong {
  font-weight: bold;
  color: #333;
}

.title-text .em {
  font-style: italic;
}

/* 格式化文本样式（降级方案） */
.formatted-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-wrap;
  word-break: break-word;
}