{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var _fs\ntry {\n  _fs = require('graceful-fs')\n} catch (_) {\n  _fs = require('fs')\n}\n\nfunction readFile (file, options, callback) {\n  if (callback == null) {\n    callback = options\n    options = {}\n  }\n\n  if (typeof options === 'string') {\n    options = {encoding: options}\n  }\n\n  options = options || {}\n  var fs = options.fs || _fs\n\n  var shouldThrow = true\n  if ('throws' in options) {\n    shouldThrow = options.throws\n  }\n\n  fs.readFile(file, options, function (err, data) {\n    if (err) return callback(err)\n\n    data = stripBom(data)\n\n    var obj\n    try {\n      obj = JSON.parse(data, options ? options.reviver : null)\n    } catch (err2) {\n      if (shouldThrow) {\n        err2.message = file + ': ' + err2.message\n        return callback(err2)\n      } else {\n        return callback(null, null)\n      }\n    }\n\n    callback(null, obj)\n  })\n}\n\nfunction readFileSync (file, options) {\n  options = options || {}\n  if (typeof options === 'string') {\n    options = {encoding: options}\n  }\n\n  var fs = options.fs || _fs\n\n  var shouldThrow = true\n  if ('throws' in options) {\n    shouldThrow = options.throws\n  }\n\n  try {\n    var content = fs.readFileSync(file, options)\n    content = stripBom(content)\n    return JSON.parse(content, options.reviver)\n  } catch (err) {\n    if (shouldThrow) {\n      err.message = file + ': ' + err.message\n      throw err\n    } else {\n      return null\n    }\n  }\n}\n\nfunction stringify (obj, options) {\n  var spaces\n  var EOL = '\\n'\n  if (typeof options === 'object' && options !== null) {\n    if (options.spaces) {\n      spaces = options.spaces\n    }\n    if (options.EOL) {\n      EOL = options.EOL\n    }\n  }\n\n  var str = JSON.stringify(obj, options ? options.replacer : null, spaces)\n\n  return str.replace(/\\n/g, EOL) + EOL\n}\n\nfunction writeFile (file, obj, options, callback) {\n  if (callback == null) {\n    callback = options\n    options = {}\n  }\n  options = options || {}\n  var fs = options.fs || _fs\n\n  var str = ''\n  try {\n    str = stringify(obj, options)\n  } catch (err) {\n    // Need to return whether a callback was passed or not\n    if (callback) callback(err, null)\n    return\n  }\n\n  fs.writeFile(file, str, options, callback)\n}\n\nfunction writeFileSync (file, obj, options) {\n  options = options || {}\n  var fs = options.fs || _fs\n\n  var str = stringify(obj, options)\n  // not sure if fs.writeFileSync returns anything, but just in case\n  return fs.writeFileSync(file, str, options)\n}\n\nfunction stripBom (content) {\n  // we do this because JSON.parse would convert it to a utf8 string if encoding wasn't specified\n  if (Buffer.isBuffer(content)) content = content.toString('utf8')\n  content = content.replace(/^\\uFEFF/, '')\n  return content\n}\n\nvar jsonfile = {\n  readFile: readFile,\n  readFileSync: readFileSync,\n  writeFile: writeFile,\n  writeFileSync: writeFileSync\n}\n\nmodule.exports = jsonfile\n"]}