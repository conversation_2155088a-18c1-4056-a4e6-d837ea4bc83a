{"version": 3, "sources": ["index.js", "fs/index.js", "copy-sync/index.js", "copy-sync/copy-sync.js", "mkdirs/index.js", "mkdirs/mkdirs.js", "mkdirs/win32.js", "mkdirs/mkdirs-sync.js", "util/utimes.js", "util/stat.js", "util/buffer.js", "copy/index.js", "copy/copy.js", "path-exists/index.js", "empty/index.js", "remove/index.js", "remove/rimraf.js", "ensure/index.js", "ensure/file.js", "ensure/link.js", "ensure/symlink.js", "ensure/symlink-paths.js", "ensure/symlink-type.js", "json/index.js", "json/jsonfile.js", "json/output-json.js", "json/output-json-sync.js", "move-sync/index.js", "move-sync/move-sync.js", "move/index.js", "move/move.js", "output/index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;AC<PERSON>,ADGA;ACFA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,AFMA,ADGA,AIZA;ADIA,AFMA,ADGA,AIZA;ADIA,AFMA,ADGA,AIZA;ADIA,AFMA,ADGA,AIZA,ACHA;AFOA,AFMA,ADGA,AIZA,ACHA;AFOA,AFMA,ADGA,AIZA,ACHA;AFOA,AFMA,ADGA,AIZA,ACHA,ACHA;AHUA,AFMA,ADGA,AIZA,ACHA,ACHA;AHUA,AFMA,ADGA,AIZA,ACHA,ACHA;AHUA,AFMA,ADGA,AIZA,AGTA,AFMA,ACHA;AHUA,AFMA,ADGA,AIZA,AGTA,AFMA,ACHA;AHUA,AFMA,ADGA,AIZA,AGTA,AFMA,ACHA;AHUA,AFMA,ADGA,AIZA,AGTA,AFMA,ACHA,AENA;ALgBA,AFMA,ADGA,AIZA,AGTA,AFMA,ACHA,AENA;ALgBA,AFMA,ADGA,AIZA,AGTA,AFMA,ACHA,AENA;ALgBA,AFMA,ADGA,AOrBA,AFMA,ACHA,AGTA,ADGA;ALgBA,AFMA,ADGA,AOrBA,AFMA,ACHA,AGTA,ADGA;ALgBA,AFMA,AMlBA,AFMA,ACHA,AGTA,ADGA;ALgBA,AFMA,AMlBA,AFMA,ACHA,AIZA,ADGA,ADGA;ALgBA,AFMA,AMlBA,AFMA,ACHA,AIZA,ADGA,ADGA;ALgBA,AFMA,AMlBA,AFMA,ACHA,AIZA,ADGA,ADGA;ALgBA,AQxBA,AV8BA,AMlBA,AFMA,ACHA,AIZA,ADGA,ADGA;ALgBA,AQxBA,AV8BA,AMlBA,AFMA,ACHA,AIZA,ADGA,ADGA;ALgBA,AQxBA,AV8BA,AMlBA,AFMA,ACHA,AIZA,ADGA,ADGA;ALgBA,AS3BA,ADGA,AV8BA,AMlBA,AFMA,ACHA,AIZA,ADGA,ADGA;ALgBA,AS3BA,ADGA,AV8BA,AMlBA,AFMA,ACHA,AIZA,ADGA,ADGA;ALgBA,AS3BA,ADGA,AV8BA,AMlBA,AFMA,ACHA,AIZA,ADGA,ADGA;ALgBA,AS3BA,ADGA,AV8BA,AMlBA,AFMA,ACHA,AOrBA,AHSA,ADGA,ADGA;ALgBA,AS3BA,AXiCA,AMlBA,AFMA,ACHA,AOrBA,AHSA,ADGA,ADGA;ALgBA,AS3BA,AXiCA,AMlBA,AFMA,ACHA,AOrBA,AHSA,ADGA,ADGA;ALgBA,AS3BA,AENA,AbuCA,AMlBA,AFMA,ACHA,AOrBA,AHSA,ADGA,ADGA;ALgBA,AS3BA,AENA,AbuCA,AMlBA,AFMA,ACHA,AOrBA,AJYA,ADGA;ALgBA,AS3BA,AENA,AbuCA,AMlBA,AFMA,AQxBA,AJYA,ADGA;ALgBA,AS3BA,AENA,AbuCA,AMlBA,AFMA,AQxBA,AENA,ANkBA,ADGA;ALgBA,AS3BA,AENA,AbuCA,AMlBA,AFMA,AQxBA,AENA,ANkBA,ADGA;ALgBA,AS3BA,AENA,AbuCA,AMlBA,AFMA,AQxBA,AENA,ANkBA,ADGA;ALgBA,AS3BA,AENA,AbuCA,AMlBA,AFMA,AQxBA,AENA,ACHA,APqBA,ADGA;ALgBA,AS3BA,AENA,AbuCA,AMlBA,AFMA,AQxBA,AENA,ACHA,APqBA,ADGA;ALgBA,AS3BA,AENA,AbuCA,AMlBA,AFMA,AQxBA,AENA,ACHA,APqBA,ADGA;ALgBA,AS3BA,AENA,AGTA,AhBgDA,AMlBA,AFMA,AQxBA,AENA,ACHA,APqBA,ADGA;ALgBA,AS3BA,AENA,AGTA,AhBgDA,AMlBA,AFMA,AU9BA,ACHA,APqBA,ADGA;ALgBA,AS3BA,AENA,AGTA,AhBgDA,AMlBA,AFMA,AU9BA,ACHA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AhBgDA,AMlBA,AFMA,AU9BA,ACHA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AhBgDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AhBgDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AlBsDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AlBsDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AlBsDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,ACHA,AnByDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,ACHA,AnByDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,ACHA,AnByDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AENA,ADGA,AnByDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AENA,ADGA,AnByDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AENA,ADGA,AnByDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AENA,ACHA,AFMA,AnByDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AENA,ACHA,AFMA,AnByDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AENA,ACHA,AFMA,AnByDA,AMlBA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AENA,ACHA,AFMA,AnByDA,AsBlEA,AhBgDA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AENA,ACHA,AFMA,AnByDA,AsBlEA,AhBgDA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AENA,ACHA,AFMA,AnByDA,AsBlEA,AhBgDA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,AjBmDA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,AjBmDA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ADGA,AENA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,AjBmDA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,ACHA,AlBsDA,AFMA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,ACHA,ApB4DA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,ACHA,ApB4DA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,AENA,ADGA,ApB4DA,AWjCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,AENA,ADGA,AT2BA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,AENA,ADGA,AT2BA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,AENA,ADGA,AENA,AXiCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,AENA,ADGA,AENA,AXiCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,AENA,ADGA,AENA,AXiCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AsBlEA,ACHA,AENA,ADGA,AENA,ACHA,AZoCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AsBlEA,AGTA,ADGA,AENA,ACHA,AZoCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AyB3EA,ADGA,AENA,ACHA,AZoCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AyB3EA,ADGA,AGTA,ACHA,AbuCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AyB3EA,ADGA,AGTA,ACHA,AbuCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AyB3EA,ADGA,AGTA,ACHA,AbuCA,APqBA,ADGA;ALgBA,AS3BA,AENA,AIZA,ACHA,AENA,ACHA,AFMA,AnByDA,AyB3EA,ADGA,AGTA,ACHA,ACHA,Ad0CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ACHA,AFMA,AnByDA,AyB3EA,ADGA,AGTA,ACHA,ACHA,Ad0CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ACHA,AFMA,AnByDA,AyB3EA,ADGA,AGTA,ACHA,ACHA,Ad0CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ACHA,AFMA,AnByDA,AyB3EA,ADGA,AGTA,ACHA,ACHA,ACHA,Af6CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ACHA,AFMA,AnByDA,AyB3EA,ADGA,AGTA,AENA,ACHA,Af6CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ACHA,AFMA,AnByDA,AyB3EA,ADGA,AGTA,AENA,ACHA,Af6CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ACHA,AFMA,AnByDA,AyB3EA,ADGA,AGTA,AENA,ACHA,Af6CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ACHA,AFMA,AnByDA,AwBxEA,AGTA,AENA,ACHA,Af6CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ADGA,AnByDA,AwBxEA,AGTA,AENA,ACHA,Af6CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ADGA,AnByDA,AwBxEA,AGTA,AENA,ACHA,Af6CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ADGA,AnByDA,AwBxEA,AGTA,AENA,ACHA,Af6CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ADGA,AnByDA,AwBxEA,AGTA,AENA,ACHA,Af6CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ADGA,AnByDA,AwBxEA,AGTA,AENA,ACHA,Af6CA,APqBA,ADGA;ALgBA,AS3BA,AMlBA,ACHA,AENA,ADGA,AnByDA,A2BjFA,AENA,ACHA,Af6CA,APqBA,ADGA;ALgBA,AS3BA,AOrBA,AENA,ADGA,AnByDA,A2BjFA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AnByDA,A2BjFA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AnByDA,A2BjFA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AnByDA,A2BjFA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AnByDA,A2BjFA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AnByDA,A2BjFA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AnByDA,A2BjFA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AnByDA,A2BjFA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AnByDA,A2BjFA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AQxBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AQxBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AQxBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AQxBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AQxBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AOrBA,AENA,ADGA,AQxBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,ADGA,AQxBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,ADGA,AQxBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,ADGA,AQxBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,ADGA,AQxBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,ADGA,AQxBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,AOrBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,AOrBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,AOrBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,AOrBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,AOrBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,AOrBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,AOrBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,AOrBA,AENA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,ACHA,Af6CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,AS3BA,Ad0CA,APqBA;ANmBA,AS3BA,AS3BA,ALeA,APqBA;ANmBA,AS3BA,AS3BA,ALeA,APqBA;ANmBA,AS3BA,AS3BA,ALeA,APqBA;ANmBA,AS3BA,AS3BA,ALeA,APqBA;ANmBA,AS3BA,AS3BA,ALeA,APqBA;ANmBA,AS3BA,AS3BA,ALeA,APqBA;ANmBA,AS3BA,AS3BA,ALeA,APqBA;ANmBA,AS3BA,AIZA,APqBA;ANmBA,AS3BA,AIZA,APqBA;ANmBA,AS3BA,AIZA,APqBA;ANmBA,AS3BA,AIZA,APqBA;ANmBA,AS3BA,AIZA,APqBA;ANmBA,AS3BA,AIZA,APqBA;ANmBA,AS3BA,AIZA,APqBA;ANmBA,AS3BA,AIZA,APqBA;ANmBA,AS3BA,AIZA,APqBA;ANmBA,AS3BA,AIZA,APqBA;ANmBA,AS3BA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA,APqBA;AGRA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nmodule.exports = Object.assign(\n  {},\n  // Export promiseified graceful-fs:\n  require('./fs'),\n  // Export extra methods:\n  require('./copy-sync'),\n  require('./copy'),\n  require('./empty'),\n  require('./ensure'),\n  require('./json'),\n  require('./mkdirs'),\n  require('./move-sync'),\n  require('./move'),\n  require('./output'),\n  require('./path-exists'),\n  require('./remove')\n)\n\n// Export fs.promises as a getter property so that we don't trigger\n// ExperimentalWarning before fs.promises is actually accessed.\nconst fs = require('fs')\nif (Object.getOwnPropertyDescriptor(fs, 'promises')) {\n  Object.defineProperty(module.exports, 'promises', {\n    get () { return fs.promises }\n  })\n}\n", "\n// This is adapted from https://github.com/normalize/mz\n// Copyright (c) 2014-2016 <PERSON> Ong <EMAIL> and Contributors\nconst u = require('universalify').fromCallback\nconst fs = require('graceful-fs')\n\nconst api = [\n  'access',\n  'appendFile',\n  'chmod',\n  'chown',\n  'close',\n  'copyFile',\n  'fchmod',\n  'fchown',\n  'fdatasync',\n  'fstat',\n  'fsync',\n  'ftruncate',\n  'futimes',\n  'lchown',\n  'lchmod',\n  'link',\n  'lstat',\n  'mkdir',\n  'mkdtemp',\n  'open',\n  'readFile',\n  'readdir',\n  'readlink',\n  'realpath',\n  'rename',\n  'rmdir',\n  'stat',\n  'symlink',\n  'truncate',\n  'unlink',\n  'utimes',\n  'writeFile'\n].filter(key => {\n  // Some commands are not available on some systems. Ex:\n  // fs.copyFile was added in Node.js v8.5.0\n  // fs.mkdtemp was added in Node.js v5.10.0\n  // fs.lchown is not available on at least some Linux\n  return typeof fs[key] === 'function'\n})\n\n// Export all keys:\nObject.keys(fs).forEach(key => {\n  if (key === 'promises') {\n    // fs.promises is a getter property that triggers ExperimentalWarning\n    // Don't re-export it here, the getter is defined in \"lib/index.js\"\n    return\n  }\n  exports[key] = fs[key]\n})\n\n// Universalify async methods:\napi.forEach(method => {\n  exports[method] = u(fs[method])\n})\n\n// We differ from mz/fs in that we still ship the old, broken, fs.exists()\n// since we are a drop-in replacement for the native module\nexports.exists = function (filename, callback) {\n  if (typeof callback === 'function') {\n    return fs.exists(filename, callback)\n  }\n  return new Promise(resolve => {\n    return fs.exists(filename, resolve)\n  })\n}\n\n// fs.read() & fs.write need special treatment due to multiple callback args\n\nexports.read = function (fd, buffer, offset, length, position, callback) {\n  if (typeof callback === 'function') {\n    return fs.read(fd, buffer, offset, length, position, callback)\n  }\n  return new Promise((resolve, reject) => {\n    fs.read(fd, buffer, offset, length, position, (err, bytesRead, buffer) => {\n      if (err) return reject(err)\n      resolve({ bytesRead, buffer })\n    })\n  })\n}\n\n// Function signature can be\n// fs.write(fd, buffer[, offset[, length[, position]]], callback)\n// OR\n// fs.write(fd, string[, position[, encoding]], callback)\n// We need to handle both cases, so we use ...args\nexports.write = function (fd, buffer, ...args) {\n  if (typeof args[args.length - 1] === 'function') {\n    return fs.write(fd, buffer, ...args)\n  }\n\n  return new Promise((resolve, reject) => {\n    fs.write(fd, buffer, ...args, (err, bytesWritten, buffer) => {\n      if (err) return reject(err)\n      resolve({ bytesWritten, buffer })\n    })\n  })\n}\n\n// fs.realpath.native only available in Node v9.2+\nif (typeof fs.realpath.native === 'function') {\n  exports.realpath.native = u(fs.realpath.native)\n}\n", "\n\nmodule.exports = {\n  copySync: require('./copy-sync')\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdirpSync = require('../mkdirs').mkdirsSync\nconst utimesSync = require('../util/utimes.js').utimesMillisSync\nconst stat = require('../util/stat')\n\nfunction copySync (src, dest, opts) {\n  if (typeof opts === 'function') {\n    opts = { filter: opts }\n  }\n\n  opts = opts || {}\n  opts.clobber = 'clobber' in opts ? !!opts.clobber : true // default to true for now\n  opts.overwrite = 'overwrite' in opts ? !!opts.overwrite : opts.clobber // overwrite falls back to clobber\n\n  // Warn about using preserveTimestamps on 32-bit node\n  if (opts.preserveTimestamps && process.arch === 'ia32') {\n    console.warn(`fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;\\n\n    see https://github.com/jprichardson/node-fs-extra/issues/269`)\n  }\n\n  const { srcStat, destStat } = stat.checkPathsSync(src, dest, 'copy')\n  stat.checkParentPathsSync(src, srcStat, dest, 'copy')\n  return handleFilterAndCopy(destStat, src, dest, opts)\n}\n\nfunction handleFilterAndCopy (destStat, src, dest, opts) {\n  if (opts.filter && !opts.filter(src, dest)) return\n  const destParent = path.dirname(dest)\n  if (!fs.existsSync(destParent)) mkdirpSync(destParent)\n  return startCopy(destStat, src, dest, opts)\n}\n\nfunction startCopy (destStat, src, dest, opts) {\n  if (opts.filter && !opts.filter(src, dest)) return\n  return getStats(destStat, src, dest, opts)\n}\n\nfunction getStats (destStat, src, dest, opts) {\n  const statSync = opts.dereference ? fs.statSync : fs.lstatSync\n  const srcStat = statSync(src)\n\n  if (srcStat.isDirectory()) return onDir(srcStat, destStat, src, dest, opts)\n  else if (srcStat.isFile() ||\n           srcStat.isCharacterDevice() ||\n           srcStat.isBlockDevice()) return onFile(srcStat, destStat, src, dest, opts)\n  else if (srcStat.isSymbolicLink()) return onLink(destStat, src, dest, opts)\n}\n\nfunction onFile (srcStat, destStat, src, dest, opts) {\n  if (!destStat) return copyFile(srcStat, src, dest, opts)\n  return mayCopyFile(srcStat, src, dest, opts)\n}\n\nfunction mayCopyFile (srcStat, src, dest, opts) {\n  if (opts.overwrite) {\n    fs.unlinkSync(dest)\n    return copyFile(srcStat, src, dest, opts)\n  } else if (opts.errorOnExist) {\n    throw new Error(`'${dest}' already exists`)\n  }\n}\n\nfunction copyFile (srcStat, src, dest, opts) {\n  if (typeof fs.copyFileSync === 'function') {\n    fs.copyFileSync(src, dest)\n    fs.chmodSync(dest, srcStat.mode)\n    if (opts.preserveTimestamps) {\n      return utimesSync(dest, srcStat.atime, srcStat.mtime)\n    }\n    return\n  }\n  return copyFileFallback(srcStat, src, dest, opts)\n}\n\nfunction copyFileFallback (srcStat, src, dest, opts) {\n  const BUF_LENGTH = 64 * 1024\n  const _buff = require('../util/buffer')(BUF_LENGTH)\n\n  const fdr = fs.openSync(src, 'r')\n  const fdw = fs.openSync(dest, 'w', srcStat.mode)\n  let pos = 0\n\n  while (pos < srcStat.size) {\n    const bytesRead = fs.readSync(fdr, _buff, 0, BUF_LENGTH, pos)\n    fs.writeSync(fdw, _buff, 0, bytesRead)\n    pos += bytesRead\n  }\n\n  if (opts.preserveTimestamps) fs.futimesSync(fdw, srcStat.atime, srcStat.mtime)\n\n  fs.closeSync(fdr)\n  fs.closeSync(fdw)\n}\n\nfunction onDir (srcStat, destStat, src, dest, opts) {\n  if (!destStat) return mkDirAndCopy(srcStat, src, dest, opts)\n  if (destStat && !destStat.isDirectory()) {\n    throw new Error(`Cannot overwrite non-directory '${dest}' with directory '${src}'.`)\n  }\n  return copyDir(src, dest, opts)\n}\n\nfunction mkDirAndCopy (srcStat, src, dest, opts) {\n  fs.mkdirSync(dest)\n  copyDir(src, dest, opts)\n  return fs.chmodSync(dest, srcStat.mode)\n}\n\nfunction copyDir (src, dest, opts) {\n  fs.readdirSync(src).forEach(item => copyDirItem(item, src, dest, opts))\n}\n\nfunction copyDirItem (item, src, dest, opts) {\n  const srcItem = path.join(src, item)\n  const destItem = path.join(dest, item)\n  const { destStat } = stat.checkPathsSync(srcItem, destItem, 'copy')\n  return startCopy(destStat, srcItem, destItem, opts)\n}\n\nfunction onLink (destStat, src, dest, opts) {\n  let resolvedSrc = fs.readlinkSync(src)\n  if (opts.dereference) {\n    resolvedSrc = path.resolve(process.cwd(), resolvedSrc)\n  }\n\n  if (!destStat) {\n    return fs.symlinkSync(resolvedSrc, dest)\n  } else {\n    let resolvedDest\n    try {\n      resolvedDest = fs.readlinkSync(dest)\n    } catch (err) {\n      // dest exists and is a regular file or directory,\n      // Windows may throw UNKNOWN error. If dest already exists,\n      // fs throws error anyway, so no need to guard against it here.\n      if (err.code === 'EINVAL' || err.code === 'UNKNOWN') return fs.symlinkSync(resolvedSrc, dest)\n      throw err\n    }\n    if (opts.dereference) {\n      resolvedDest = path.resolve(process.cwd(), resolvedDest)\n    }\n    if (stat.isSrcSubdir(resolvedSrc, resolvedDest)) {\n      throw new Error(`Cannot copy '${resolvedSrc}' to a subdirectory of itself, '${resolvedDest}'.`)\n    }\n\n    // prevent copy if src is a subdir of dest since unlinking\n    // dest in this case would result in removing src contents\n    // and therefore a broken symlink would be created.\n    if (fs.statSync(dest).isDirectory() && stat.isSrcSubdir(resolvedDest, resolvedSrc)) {\n      throw new Error(`Cannot overwrite '${resolvedDest}' with '${resolvedSrc}'.`)\n    }\n    return copyLink(resolvedSrc, dest)\n  }\n}\n\nfunction copyLink (resolvedSrc, dest) {\n  fs.unlinkSync(dest)\n  return fs.symlinkSync(resolvedSrc, dest)\n}\n\nmodule.exports = copySync\n", "\nconst u = require('universalify').fromCallback\nconst mkdirs = u(require('./mkdirs'))\nconst mkdirsSync = require('./mkdirs-sync')\n\nmodule.exports = {\n  mkdirs,\n  mkdirsSync,\n  // alias\n  mkdirp: mkdirs,\n  mkdirpSync: mkdirsSync,\n  ensureDir: mkdirs,\n  ensureDirSync: mkdirsSync\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst invalidWin32Path = require('./win32').invalidWin32Path\n\nconst o777 = parseInt('0777', 8)\n\nfunction mkdirs (p, opts, callback, made) {\n  if (typeof opts === 'function') {\n    callback = opts\n    opts = {}\n  } else if (!opts || typeof opts !== 'object') {\n    opts = { mode: opts }\n  }\n\n  if (process.platform === 'win32' && invalidWin32Path(p)) {\n    const errInval = new Error(p + ' contains invalid WIN32 path characters.')\n    errInval.code = 'EINVAL'\n    return callback(errInval)\n  }\n\n  let mode = opts.mode\n  const xfs = opts.fs || fs\n\n  if (mode === undefined) {\n    mode = o777 & (~process.umask())\n  }\n  if (!made) made = null\n\n  callback = callback || function () {}\n  p = path.resolve(p)\n\n  xfs.mkdir(p, mode, er => {\n    if (!er) {\n      made = made || p\n      return callback(null, made)\n    }\n    switch (er.code) {\n      case 'ENOENT':\n        if (path.dirname(p) === p) return callback(er)\n        mkdirs(path.dirname(p), opts, (er, made) => {\n          if (er) callback(er, made)\n          else mkdirs(p, opts, callback, made)\n        })\n        break\n\n      // In the case of any other error, just see if there's a dir\n      // there already.  If so, then hooray!  If not, then something\n      // is borked.\n      default:\n        xfs.stat(p, (er2, stat) => {\n          // if the stat fails, then that's super weird.\n          // let the original error be the failure reason.\n          if (er2 || !stat.isDirectory()) callback(er, made)\n          else callback(null, made)\n        })\n        break\n    }\n  })\n}\n\nmodule.exports = mkdirs\n", "\n\nconst path = require('path')\n\n// get drive on windows\nfunction getRootPath (p) {\n  p = path.normalize(path.resolve(p)).split(path.sep)\n  if (p.length > 0) return p[0]\n  return null\n}\n\n// http://stackoverflow.com/a/62888/10333 contains more accurate\n// TODO: expand to include the rest\nconst INVALID_PATH_CHARS = /[<>:\"|?*]/\n\nfunction invalidWin32Path (p) {\n  const rp = getRootPath(p)\n  p = p.replace(rp, '')\n  return INVALID_PATH_CHARS.test(p)\n}\n\nmodule.exports = {\n  getRootPath,\n  invalidWin32Path\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst invalidWin32Path = require('./win32').invalidWin32Path\n\nconst o777 = parseInt('0777', 8)\n\nfunction mkdirsSync (p, opts, made) {\n  if (!opts || typeof opts !== 'object') {\n    opts = { mode: opts }\n  }\n\n  let mode = opts.mode\n  const xfs = opts.fs || fs\n\n  if (process.platform === 'win32' && invalidWin32Path(p)) {\n    const errInval = new Error(p + ' contains invalid WIN32 path characters.')\n    errInval.code = 'EINVAL'\n    throw errInval\n  }\n\n  if (mode === undefined) {\n    mode = o777 & (~process.umask())\n  }\n  if (!made) made = null\n\n  p = path.resolve(p)\n\n  try {\n    xfs.mkdirSync(p, mode)\n    made = made || p\n  } catch (err0) {\n    if (err0.code === 'ENOENT') {\n      if (path.dirname(p) === p) throw err0\n      made = mkdirsSync(path.dirname(p), opts, made)\n      mkdirsSync(p, opts, made)\n    } else {\n      // In the case of any other error, just see if there's a dir there\n      // already. If so, then hooray!  If not, then something is borked.\n      let stat\n      try {\n        stat = xfs.statSync(p)\n      } catch (err1) {\n        throw err0\n      }\n      if (!stat.isDirectory()) throw err0\n    }\n  }\n\n  return made\n}\n\nmodule.exports = mkdirsSync\n", "\n\nconst fs = require('graceful-fs')\nconst os = require('os')\nconst path = require('path')\n\n// HFS, ext{2,3}, FAT do not, Node.js v0.10 does not\nfunction hasMillisResSync () {\n  let tmpfile = path.join('millis-test-sync' + Date.now().toString() + Math.random().toString().slice(2))\n  tmpfile = path.join(os.tmpdir(), tmpfile)\n\n  // 550 millis past UNIX epoch\n  const d = new Date(1435410243862)\n  fs.writeFileSync(tmpfile, 'https://github.com/jprichardson/node-fs-extra/pull/141')\n  const fd = fs.openSync(tmpfile, 'r+')\n  fs.futimesSync(fd, d, d)\n  fs.closeSync(fd)\n  return fs.statSync(tmpfile).mtime > 1435410243000\n}\n\nfunction hasMillisRes (callback) {\n  let tmpfile = path.join('millis-test' + Date.now().toString() + Math.random().toString().slice(2))\n  tmpfile = path.join(os.tmpdir(), tmpfile)\n\n  // 550 millis past UNIX epoch\n  const d = new Date(1435410243862)\n  fs.writeFile(tmpfile, 'https://github.com/jprichardson/node-fs-extra/pull/141', err => {\n    if (err) return callback(err)\n    fs.open(tmpfile, 'r+', (err, fd) => {\n      if (err) return callback(err)\n      fs.futimes(fd, d, d, err => {\n        if (err) return callback(err)\n        fs.close(fd, err => {\n          if (err) return callback(err)\n          fs.stat(tmpfile, (err, stats) => {\n            if (err) return callback(err)\n            callback(null, stats.mtime > 1435410243000)\n          })\n        })\n      })\n    })\n  })\n}\n\nfunction timeRemoveMillis (timestamp) {\n  if (typeof timestamp === 'number') {\n    return Math.floor(timestamp / 1000) * 1000\n  } else if (timestamp instanceof Date) {\n    return new Date(Math.floor(timestamp.getTime() / 1000) * 1000)\n  } else {\n    throw new Error('fs-extra: timeRemoveMillis() unknown parameter type')\n  }\n}\n\nfunction utimesMillis (path, atime, mtime, callback) {\n  // if (!HAS_MILLIS_RES) return fs.utimes(path, atime, mtime, callback)\n  fs.open(path, 'r+', (err, fd) => {\n    if (err) return callback(err)\n    fs.futimes(fd, atime, mtime, futimesErr => {\n      fs.close(fd, closeErr => {\n        if (callback) callback(futimesErr || closeErr)\n      })\n    })\n  })\n}\n\nfunction utimesMillisSync (path, atime, mtime) {\n  const fd = fs.openSync(path, 'r+')\n  fs.futimesSync(fd, atime, mtime)\n  return fs.closeSync(fd)\n}\n\nmodule.exports = {\n  hasMillisRes,\n  hasMillisResSync,\n  timeRemoveMillis,\n  utimesMillis,\n  utimesMillisSync\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\n\nconst NODE_VERSION_MAJOR_WITH_BIGINT = 10\nconst NODE_VERSION_MINOR_WITH_BIGINT = 5\nconst NODE_VERSION_PATCH_WITH_BIGINT = 0\nconst nodeVersion = process.versions.node.split('.')\nconst nodeVersionMajor = Number.parseInt(nodeVersion[0], 10)\nconst nodeVersionMinor = Number.parseInt(nodeVersion[1], 10)\nconst nodeVersionPatch = Number.parseInt(nodeVersion[2], 10)\n\nfunction nodeSupportsBigInt () {\n  if (nodeVersionMajor > NODE_VERSION_MAJOR_WITH_BIGINT) {\n    return true\n  } else if (nodeVersionMajor === NODE_VERSION_MAJOR_WITH_BIGINT) {\n    if (nodeVersionMinor > NODE_VERSION_MINOR_WITH_BIGINT) {\n      return true\n    } else if (nodeVersionMinor === NODE_VERSION_MINOR_WITH_BIGINT) {\n      if (nodeVersionPatch >= NODE_VERSION_PATCH_WITH_BIGINT) {\n        return true\n      }\n    }\n  }\n  return false\n}\n\nfunction getStats (src, dest, cb) {\n  if (nodeSupportsBigInt()) {\n    fs.stat(src, { bigint: true }, (err, srcStat) => {\n      if (err) return cb(err)\n      fs.stat(dest, { bigint: true }, (err, destStat) => {\n        if (err) {\n          if (err.code === 'ENOENT') return cb(null, { srcStat, destStat: null })\n          return cb(err)\n        }\n        return cb(null, { srcStat, destStat })\n      })\n    })\n  } else {\n    fs.stat(src, (err, srcStat) => {\n      if (err) return cb(err)\n      fs.stat(dest, (err, destStat) => {\n        if (err) {\n          if (err.code === 'ENOENT') return cb(null, { srcStat, destStat: null })\n          return cb(err)\n        }\n        return cb(null, { srcStat, destStat })\n      })\n    })\n  }\n}\n\nfunction getStatsSync (src, dest) {\n  let srcStat, destStat\n  if (nodeSupportsBigInt()) {\n    srcStat = fs.statSync(src, { bigint: true })\n  } else {\n    srcStat = fs.statSync(src)\n  }\n  try {\n    if (nodeSupportsBigInt()) {\n      destStat = fs.statSync(dest, { bigint: true })\n    } else {\n      destStat = fs.statSync(dest)\n    }\n  } catch (err) {\n    if (err.code === 'ENOENT') return { srcStat, destStat: null }\n    throw err\n  }\n  return { srcStat, destStat }\n}\n\nfunction checkPaths (src, dest, funcName, cb) {\n  getStats(src, dest, (err, stats) => {\n    if (err) return cb(err)\n    const { srcStat, destStat } = stats\n    if (destStat && destStat.ino && destStat.dev && destStat.ino === srcStat.ino && destStat.dev === srcStat.dev) {\n      return cb(new Error('Source and destination must not be the same.'))\n    }\n    if (srcStat.isDirectory() && isSrcSubdir(src, dest)) {\n      return cb(new Error(errMsg(src, dest, funcName)))\n    }\n    return cb(null, { srcStat, destStat })\n  })\n}\n\nfunction checkPathsSync (src, dest, funcName) {\n  const { srcStat, destStat } = getStatsSync(src, dest)\n  if (destStat && destStat.ino && destStat.dev && destStat.ino === srcStat.ino && destStat.dev === srcStat.dev) {\n    throw new Error('Source and destination must not be the same.')\n  }\n  if (srcStat.isDirectory() && isSrcSubdir(src, dest)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n  return { srcStat, destStat }\n}\n\n// recursively check if dest parent is a subdirectory of src.\n// It works for all file types including symlinks since it\n// checks the src and dest inodes. It starts from the deepest\n// parent and stops once it reaches the src parent or the root path.\nfunction checkParentPaths (src, srcStat, dest, funcName, cb) {\n  const srcParent = path.resolve(path.dirname(src))\n  const destParent = path.resolve(path.dirname(dest))\n  if (destParent === srcParent || destParent === path.parse(destParent).root) return cb()\n  if (nodeSupportsBigInt()) {\n    fs.stat(destParent, { bigint: true }, (err, destStat) => {\n      if (err) {\n        if (err.code === 'ENOENT') return cb()\n        return cb(err)\n      }\n      if (destStat.ino && destStat.dev && destStat.ino === srcStat.ino && destStat.dev === srcStat.dev) {\n        return cb(new Error(errMsg(src, dest, funcName)))\n      }\n      return checkParentPaths(src, srcStat, destParent, funcName, cb)\n    })\n  } else {\n    fs.stat(destParent, (err, destStat) => {\n      if (err) {\n        if (err.code === 'ENOENT') return cb()\n        return cb(err)\n      }\n      if (destStat.ino && destStat.dev && destStat.ino === srcStat.ino && destStat.dev === srcStat.dev) {\n        return cb(new Error(errMsg(src, dest, funcName)))\n      }\n      return checkParentPaths(src, srcStat, destParent, funcName, cb)\n    })\n  }\n}\n\nfunction checkParentPathsSync (src, srcStat, dest, funcName) {\n  const srcParent = path.resolve(path.dirname(src))\n  const destParent = path.resolve(path.dirname(dest))\n  if (destParent === srcParent || destParent === path.parse(destParent).root) return\n  let destStat\n  try {\n    if (nodeSupportsBigInt()) {\n      destStat = fs.statSync(destParent, { bigint: true })\n    } else {\n      destStat = fs.statSync(destParent)\n    }\n  } catch (err) {\n    if (err.code === 'ENOENT') return\n    throw err\n  }\n  if (destStat.ino && destStat.dev && destStat.ino === srcStat.ino && destStat.dev === srcStat.dev) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n  return checkParentPathsSync(src, srcStat, destParent, funcName)\n}\n\n// return true if dest is a subdir of src, otherwise false.\n// It only checks the path strings.\nfunction isSrcSubdir (src, dest) {\n  const srcArr = path.resolve(src).split(path.sep).filter(i => i)\n  const destArr = path.resolve(dest).split(path.sep).filter(i => i)\n  return srcArr.reduce((acc, cur, i) => acc && destArr[i] === cur, true)\n}\n\nfunction errMsg (src, dest, funcName) {\n  return `Cannot ${funcName} '${src}' to a subdirectory of itself, '${dest}'.`\n}\n\nmodule.exports = {\n  checkPaths,\n  checkPathsSync,\n  checkParentPaths,\n  checkParentPathsSync,\n  isSrcSubdir\n}\n", "\n/* eslint-disable node/no-deprecated-api */\nmodule.exports = function (size) {\n  if (typeof Buffer.allocUnsafe === 'function') {\n    try {\n      return Buffer.allocUnsafe(size)\n    } catch (e) {\n      return new Buffer(size)\n    }\n  }\n  return new Buffer(size)\n}\n", "\n\nconst u = require('universalify').fromCallback\nmodule.exports = {\n  copy: u(require('./copy'))\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdirp = require('../mkdirs').mkdirs\nconst pathExists = require('../path-exists').pathExists\nconst utimes = require('../util/utimes').utimesMillis\nconst stat = require('../util/stat')\n\nfunction copy (src, dest, opts, cb) {\n  if (typeof opts === 'function' && !cb) {\n    cb = opts\n    opts = {}\n  } else if (typeof opts === 'function') {\n    opts = { filter: opts }\n  }\n\n  cb = cb || function () {}\n  opts = opts || {}\n\n  opts.clobber = 'clobber' in opts ? !!opts.clobber : true // default to true for now\n  opts.overwrite = 'overwrite' in opts ? !!opts.overwrite : opts.clobber // overwrite falls back to clobber\n\n  // Warn about using preserveTimestamps on 32-bit node\n  if (opts.preserveTimestamps && process.arch === 'ia32') {\n    console.warn(`fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;\\n\n    see https://github.com/jprichardson/node-fs-extra/issues/269`)\n  }\n\n  stat.checkPaths(src, dest, 'copy', (err, stats) => {\n    if (err) return cb(err)\n    const { srcStat, destStat } = stats\n    stat.checkParentPaths(src, srcStat, dest, 'copy', err => {\n      if (err) return cb(err)\n      if (opts.filter) return handleFilter(checkParentDir, destStat, src, dest, opts, cb)\n      return checkParentDir(destStat, src, dest, opts, cb)\n    })\n  })\n}\n\nfunction checkParentDir (destStat, src, dest, opts, cb) {\n  const destParent = path.dirname(dest)\n  pathExists(destParent, (err, dirExists) => {\n    if (err) return cb(err)\n    if (dirExists) return startCopy(destStat, src, dest, opts, cb)\n    mkdirp(destParent, err => {\n      if (err) return cb(err)\n      return startCopy(destStat, src, dest, opts, cb)\n    })\n  })\n}\n\nfunction handleFilter (onInclude, destStat, src, dest, opts, cb) {\n  Promise.resolve(opts.filter(src, dest)).then(include => {\n    if (include) return onInclude(destStat, src, dest, opts, cb)\n    return cb()\n  }, error => cb(error))\n}\n\nfunction startCopy (destStat, src, dest, opts, cb) {\n  if (opts.filter) return handleFilter(getStats, destStat, src, dest, opts, cb)\n  return getStats(destStat, src, dest, opts, cb)\n}\n\nfunction getStats (destStat, src, dest, opts, cb) {\n  const stat = opts.dereference ? fs.stat : fs.lstat\n  stat(src, (err, srcStat) => {\n    if (err) return cb(err)\n\n    if (srcStat.isDirectory()) return onDir(srcStat, destStat, src, dest, opts, cb)\n    else if (srcStat.isFile() ||\n             srcStat.isCharacterDevice() ||\n             srcStat.isBlockDevice()) return onFile(srcStat, destStat, src, dest, opts, cb)\n    else if (srcStat.isSymbolicLink()) return onLink(destStat, src, dest, opts, cb)\n  })\n}\n\nfunction onFile (srcStat, destStat, src, dest, opts, cb) {\n  if (!destStat) return copyFile(srcStat, src, dest, opts, cb)\n  return mayCopyFile(srcStat, src, dest, opts, cb)\n}\n\nfunction mayCopyFile (srcStat, src, dest, opts, cb) {\n  if (opts.overwrite) {\n    fs.unlink(dest, err => {\n      if (err) return cb(err)\n      return copyFile(srcStat, src, dest, opts, cb)\n    })\n  } else if (opts.errorOnExist) {\n    return cb(new Error(`'${dest}' already exists`))\n  } else return cb()\n}\n\nfunction copyFile (srcStat, src, dest, opts, cb) {\n  if (typeof fs.copyFile === 'function') {\n    return fs.copyFile(src, dest, err => {\n      if (err) return cb(err)\n      return setDestModeAndTimestamps(srcStat, dest, opts, cb)\n    })\n  }\n  return copyFileFallback(srcStat, src, dest, opts, cb)\n}\n\nfunction copyFileFallback (srcStat, src, dest, opts, cb) {\n  const rs = fs.createReadStream(src)\n  rs.on('error', err => cb(err)).once('open', () => {\n    const ws = fs.createWriteStream(dest, { mode: srcStat.mode })\n    ws.on('error', err => cb(err))\n      .on('open', () => rs.pipe(ws))\n      .once('close', () => setDestModeAndTimestamps(srcStat, dest, opts, cb))\n  })\n}\n\nfunction setDestModeAndTimestamps (srcStat, dest, opts, cb) {\n  fs.chmod(dest, srcStat.mode, err => {\n    if (err) return cb(err)\n    if (opts.preserveTimestamps) {\n      return utimes(dest, srcStat.atime, srcStat.mtime, cb)\n    }\n    return cb()\n  })\n}\n\nfunction onDir (srcStat, destStat, src, dest, opts, cb) {\n  if (!destStat) return mkDirAndCopy(srcStat, src, dest, opts, cb)\n  if (destStat && !destStat.isDirectory()) {\n    return cb(new Error(`Cannot overwrite non-directory '${dest}' with directory '${src}'.`))\n  }\n  return copyDir(src, dest, opts, cb)\n}\n\nfunction mkDirAndCopy (srcStat, src, dest, opts, cb) {\n  fs.mkdir(dest, err => {\n    if (err) return cb(err)\n    copyDir(src, dest, opts, err => {\n      if (err) return cb(err)\n      return fs.chmod(dest, srcStat.mode, cb)\n    })\n  })\n}\n\nfunction copyDir (src, dest, opts, cb) {\n  fs.readdir(src, (err, items) => {\n    if (err) return cb(err)\n    return copyDirItems(items, src, dest, opts, cb)\n  })\n}\n\nfunction copyDirItems (items, src, dest, opts, cb) {\n  const item = items.pop()\n  if (!item) return cb()\n  return copyDirItem(items, item, src, dest, opts, cb)\n}\n\nfunction copyDirItem (items, item, src, dest, opts, cb) {\n  const srcItem = path.join(src, item)\n  const destItem = path.join(dest, item)\n  stat.checkPaths(srcItem, destItem, 'copy', (err, stats) => {\n    if (err) return cb(err)\n    const { destStat } = stats\n    startCopy(destStat, srcItem, destItem, opts, err => {\n      if (err) return cb(err)\n      return copyDirItems(items, src, dest, opts, cb)\n    })\n  })\n}\n\nfunction onLink (destStat, src, dest, opts, cb) {\n  fs.readlink(src, (err, resolvedSrc) => {\n    if (err) return cb(err)\n    if (opts.dereference) {\n      resolvedSrc = path.resolve(process.cwd(), resolvedSrc)\n    }\n\n    if (!destStat) {\n      return fs.symlink(resolvedSrc, dest, cb)\n    } else {\n      fs.readlink(dest, (err, resolvedDest) => {\n        if (err) {\n          // dest exists and is a regular file or directory,\n          // Windows may throw UNKNOWN error. If dest already exists,\n          // fs throws error anyway, so no need to guard against it here.\n          if (err.code === 'EINVAL' || err.code === 'UNKNOWN') return fs.symlink(resolvedSrc, dest, cb)\n          return cb(err)\n        }\n        if (opts.dereference) {\n          resolvedDest = path.resolve(process.cwd(), resolvedDest)\n        }\n        if (stat.isSrcSubdir(resolvedSrc, resolvedDest)) {\n          return cb(new Error(`Cannot copy '${resolvedSrc}' to a subdirectory of itself, '${resolvedDest}'.`))\n        }\n\n        // do not copy if src is a subdir of dest since unlinking\n        // dest in this case would result in removing src contents\n        // and therefore a broken symlink would be created.\n        if (destStat.isDirectory() && stat.isSrcSubdir(resolvedDest, resolvedSrc)) {\n          return cb(new Error(`Cannot overwrite '${resolvedDest}' with '${resolvedSrc}'.`))\n        }\n        return copyLink(resolvedSrc, dest, cb)\n      })\n    }\n  })\n}\n\nfunction copyLink (resolvedSrc, dest, cb) {\n  fs.unlink(dest, err => {\n    if (err) return cb(err)\n    return fs.symlink(resolvedSrc, dest, cb)\n  })\n}\n\nmodule.exports = copy\n", "\nconst u = require('universalify').fromPromise\nconst fs = require('../fs')\n\nfunction pathExists (path) {\n  return fs.access(path).then(() => true).catch(() => false)\n}\n\nmodule.exports = {\n  pathExists: u(pathExists),\n  pathExistsSync: fs.existsSync\n}\n", "\n\nconst u = require('universalify').fromCallback\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdir = require('../mkdirs')\nconst remove = require('../remove')\n\nconst emptyDir = u(function emptyDir (dir, callback) {\n  callback = callback || function () {}\n  fs.readdir(dir, (err, items) => {\n    if (err) return mkdir.mkdirs(dir, callback)\n\n    items = items.map(item => path.join(dir, item))\n\n    deleteItem()\n\n    function deleteItem () {\n      const item = items.pop()\n      if (!item) return callback()\n      remove.remove(item, err => {\n        if (err) return callback(err)\n        deleteItem()\n      })\n    }\n  })\n})\n\nfunction emptyDirSync (dir) {\n  let items\n  try {\n    items = fs.readdirSync(dir)\n  } catch (err) {\n    return mkdir.mkdirsSync(dir)\n  }\n\n  items.forEach(item => {\n    item = path.join(dir, item)\n    remove.removeSync(item)\n  })\n}\n\nmodule.exports = {\n  emptyDirSync,\n  emptydirSync: emptyDirSync,\n  emptyDir,\n  emptydir: emptyDir\n}\n", "\n\nconst u = require('universalify').fromCallback\nconst rimraf = require('./rimraf')\n\nmodule.exports = {\n  remove: u(rimraf),\n  removeSync: rimraf.sync\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst assert = require('assert')\n\nconst isWindows = (process.platform === 'win32')\n\nfunction defaults (options) {\n  const methods = [\n    'unlink',\n    'chmod',\n    'stat',\n    'lstat',\n    'rmdir',\n    'readdir'\n  ]\n  methods.forEach(m => {\n    options[m] = options[m] || fs[m]\n    m = m + 'Sync'\n    options[m] = options[m] || fs[m]\n  })\n\n  options.maxBusyTries = options.maxBusyTries || 3\n}\n\nfunction rimraf (p, options, cb) {\n  let busyTries = 0\n\n  if (typeof options === 'function') {\n    cb = options\n    options = {}\n  }\n\n  assert(p, 'rimraf: missing path')\n  assert.strictEqual(typeof p, 'string', 'rimraf: path should be a string')\n  assert.strictEqual(typeof cb, 'function', 'rimraf: callback function required')\n  assert(options, 'rimraf: invalid options argument provided')\n  assert.strictEqual(typeof options, 'object', 'rimraf: options should be object')\n\n  defaults(options)\n\n  rimraf_(p, options, function CB (er) {\n    if (er) {\n      if ((er.code === 'EBUSY' || er.code === 'ENOTEMPTY' || er.code === 'EPERM') &&\n          busyTries < options.maxBusyTries) {\n        busyTries++\n        const time = busyTries * 100\n        // try again, with the same exact callback as this one.\n        return setTimeout(() => rimraf_(p, options, CB), time)\n      }\n\n      // already gone\n      if (er.code === 'ENOENT') er = null\n    }\n\n    cb(er)\n  })\n}\n\n// Two possible strategies.\n// 1. Assume it's a file.  unlink it, then do the dir stuff on EPERM or EISDIR\n// 2. Assume it's a directory.  readdir, then do the file stuff on ENOTDIR\n//\n// Both result in an extra syscall when you guess wrong.  However, there\n// are likely far more normal files in the world than directories.  This\n// is based on the assumption that a the average number of files per\n// directory is >= 1.\n//\n// If anyone ever complains about this, then I guess the strategy could\n// be made configurable somehow.  But until then, YAGNI.\nfunction rimraf_ (p, options, cb) {\n  assert(p)\n  assert(options)\n  assert(typeof cb === 'function')\n\n  // sunos lets the root user unlink directories, which is... weird.\n  // so we have to lstat here and make sure it's not a dir.\n  options.lstat(p, (er, st) => {\n    if (er && er.code === 'ENOENT') {\n      return cb(null)\n    }\n\n    // Windows can EPERM on stat.  Life is suffering.\n    if (er && er.code === 'EPERM' && isWindows) {\n      return fixWinEPERM(p, options, er, cb)\n    }\n\n    if (st && st.isDirectory()) {\n      return rmdir(p, options, er, cb)\n    }\n\n    options.unlink(p, er => {\n      if (er) {\n        if (er.code === 'ENOENT') {\n          return cb(null)\n        }\n        if (er.code === 'EPERM') {\n          return (isWindows)\n            ? fixWinEPERM(p, options, er, cb)\n            : rmdir(p, options, er, cb)\n        }\n        if (er.code === 'EISDIR') {\n          return rmdir(p, options, er, cb)\n        }\n      }\n      return cb(er)\n    })\n  })\n}\n\nfunction fixWinEPERM (p, options, er, cb) {\n  assert(p)\n  assert(options)\n  assert(typeof cb === 'function')\n  if (er) {\n    assert(er instanceof Error)\n  }\n\n  options.chmod(p, 0o666, er2 => {\n    if (er2) {\n      cb(er2.code === 'ENOENT' ? null : er)\n    } else {\n      options.stat(p, (er3, stats) => {\n        if (er3) {\n          cb(er3.code === 'ENOENT' ? null : er)\n        } else if (stats.isDirectory()) {\n          rmdir(p, options, er, cb)\n        } else {\n          options.unlink(p, cb)\n        }\n      })\n    }\n  })\n}\n\nfunction fixWinEPERMSync (p, options, er) {\n  let stats\n\n  assert(p)\n  assert(options)\n  if (er) {\n    assert(er instanceof Error)\n  }\n\n  try {\n    options.chmodSync(p, 0o666)\n  } catch (er2) {\n    if (er2.code === 'ENOENT') {\n      return\n    } else {\n      throw er\n    }\n  }\n\n  try {\n    stats = options.statSync(p)\n  } catch (er3) {\n    if (er3.code === 'ENOENT') {\n      return\n    } else {\n      throw er\n    }\n  }\n\n  if (stats.isDirectory()) {\n    rmdirSync(p, options, er)\n  } else {\n    options.unlinkSync(p)\n  }\n}\n\nfunction rmdir (p, options, originalEr, cb) {\n  assert(p)\n  assert(options)\n  if (originalEr) {\n    assert(originalEr instanceof Error)\n  }\n  assert(typeof cb === 'function')\n\n  // try to rmdir first, and only readdir on ENOTEMPTY or EEXIST (SunOS)\n  // if we guessed wrong, and it's not a directory, then\n  // raise the original error.\n  options.rmdir(p, er => {\n    if (er && (er.code === 'ENOTEMPTY' || er.code === 'EEXIST' || er.code === 'EPERM')) {\n      rmkids(p, options, cb)\n    } else if (er && er.code === 'ENOTDIR') {\n      cb(originalEr)\n    } else {\n      cb(er)\n    }\n  })\n}\n\nfunction rmkids (p, options, cb) {\n  assert(p)\n  assert(options)\n  assert(typeof cb === 'function')\n\n  options.readdir(p, (er, files) => {\n    if (er) return cb(er)\n\n    let n = files.length\n    let errState\n\n    if (n === 0) return options.rmdir(p, cb)\n\n    files.forEach(f => {\n      rimraf(path.join(p, f), options, er => {\n        if (errState) {\n          return\n        }\n        if (er) return cb(errState = er)\n        if (--n === 0) {\n          options.rmdir(p, cb)\n        }\n      })\n    })\n  })\n}\n\n// this looks simpler, and is strictly *faster*, but will\n// tie up the JavaScript thread and fail on excessively\n// deep directory trees.\nfunction rimrafSync (p, options) {\n  let st\n\n  options = options || {}\n  defaults(options)\n\n  assert(p, 'rimraf: missing path')\n  assert.strictEqual(typeof p, 'string', 'rimraf: path should be a string')\n  assert(options, 'rimraf: missing options')\n  assert.strictEqual(typeof options, 'object', 'rimraf: options should be object')\n\n  try {\n    st = options.lstatSync(p)\n  } catch (er) {\n    if (er.code === 'ENOENT') {\n      return\n    }\n\n    // Windows can EPERM on stat.  Life is suffering.\n    if (er.code === 'EPERM' && isWindows) {\n      fixWinEPERMSync(p, options, er)\n    }\n  }\n\n  try {\n    // sunos lets the root user unlink directories, which is... weird.\n    if (st && st.isDirectory()) {\n      rmdirSync(p, options, null)\n    } else {\n      options.unlinkSync(p)\n    }\n  } catch (er) {\n    if (er.code === 'ENOENT') {\n      return\n    } else if (er.code === 'EPERM') {\n      return isWindows ? fixWinEPERMSync(p, options, er) : rmdirSync(p, options, er)\n    } else if (er.code !== 'EISDIR') {\n      throw er\n    }\n    rmdirSync(p, options, er)\n  }\n}\n\nfunction rmdirSync (p, options, originalEr) {\n  assert(p)\n  assert(options)\n  if (originalEr) {\n    assert(originalEr instanceof Error)\n  }\n\n  try {\n    options.rmdirSync(p)\n  } catch (er) {\n    if (er.code === 'ENOTDIR') {\n      throw originalEr\n    } else if (er.code === 'ENOTEMPTY' || er.code === 'EEXIST' || er.code === 'EPERM') {\n      rmkidsSync(p, options)\n    } else if (er.code !== 'ENOENT') {\n      throw er\n    }\n  }\n}\n\nfunction rmkidsSync (p, options) {\n  assert(p)\n  assert(options)\n  options.readdirSync(p).forEach(f => rimrafSync(path.join(p, f), options))\n\n  if (isWindows) {\n    // We only end up here once we got ENOTEMPTY at least once, and\n    // at this point, we are guaranteed to have removed all the kids.\n    // So, we know that it won't be ENOENT or ENOTDIR or anything else.\n    // try really hard to delete stuff on windows, because it has a\n    // PROFOUNDLY annoying habit of not closing handles promptly when\n    // files are deleted, resulting in spurious ENOTEMPTY errors.\n    const startTime = Date.now()\n    do {\n      try {\n        const ret = options.rmdirSync(p, options)\n        return ret\n      } catch (er) { }\n    } while (Date.now() - startTime < 500) // give up after 500ms\n  } else {\n    const ret = options.rmdirSync(p, options)\n    return ret\n  }\n}\n\nmodule.exports = rimraf\nrimraf.sync = rimrafSync\n", "\n\nconst file = require('./file')\nconst link = require('./link')\nconst symlink = require('./symlink')\n\nmodule.exports = {\n  // file\n  createFile: file.createFile,\n  createFileSync: file.createFileSync,\n  ensureFile: file.createFile,\n  ensureFileSync: file.createFileSync,\n  // link\n  createLink: link.createLink,\n  createLinkSync: link.createLinkSync,\n  ensureLink: link.createLink,\n  ensureLinkSync: link.createLinkSync,\n  // symlink\n  createSymlink: symlink.createSymlink,\n  createSymlinkSync: symlink.createSymlinkSync,\n  ensureSymlink: symlink.createSymlink,\n  ensureSymlinkSync: symlink.createSymlinkSync\n}\n", "\n\nconst u = require('universalify').fromCallback\nconst path = require('path')\nconst fs = require('graceful-fs')\nconst mkdir = require('../mkdirs')\nconst pathExists = require('../path-exists').pathExists\n\nfunction createFile (file, callback) {\n  function makeFile () {\n    fs.writeFile(file, '', err => {\n      if (err) return callback(err)\n      callback()\n    })\n  }\n\n  fs.stat(file, (err, stats) => { // eslint-disable-line handle-callback-err\n    if (!err && stats.isFile()) return callback()\n    const dir = path.dirname(file)\n    pathExists(dir, (err, dirExists) => {\n      if (err) return callback(err)\n      if (dirExists) return makeFile()\n      mkdir.mkdirs(dir, err => {\n        if (err) return callback(err)\n        makeFile()\n      })\n    })\n  })\n}\n\nfunction createFileSync (file) {\n  let stats\n  try {\n    stats = fs.statSync(file)\n  } catch (e) {}\n  if (stats && stats.isFile()) return\n\n  const dir = path.dirname(file)\n  if (!fs.existsSync(dir)) {\n    mkdir.mkdirsSync(dir)\n  }\n\n  fs.writeFileSync(file, '')\n}\n\nmodule.exports = {\n  createFile: u(createFile),\n  createFileSync\n}\n", "\n\nconst u = require('universalify').fromCallback\nconst path = require('path')\nconst fs = require('graceful-fs')\nconst mkdir = require('../mkdirs')\nconst pathExists = require('../path-exists').pathExists\n\nfunction createLink (srcpath, dstpath, callback) {\n  function makeLink (srcpath, dstpath) {\n    fs.link(srcpath, dstpath, err => {\n      if (err) return callback(err)\n      callback(null)\n    })\n  }\n\n  pathExists(dstpath, (err, destinationExists) => {\n    if (err) return callback(err)\n    if (destinationExists) return callback(null)\n    fs.lstat(srcpath, (err) => {\n      if (err) {\n        err.message = err.message.replace('lstat', 'ensureLink')\n        return callback(err)\n      }\n\n      const dir = path.dirname(dstpath)\n      pathExists(dir, (err, dirExists) => {\n        if (err) return callback(err)\n        if (dirExists) return makeLink(srcpath, dstpath)\n        mkdir.mkdirs(dir, err => {\n          if (err) return callback(err)\n          makeLink(srcpath, dstpath)\n        })\n      })\n    })\n  })\n}\n\nfunction createLinkSync (srcpath, dstpath) {\n  const destinationExists = fs.existsSync(dstpath)\n  if (destinationExists) return undefined\n\n  try {\n    fs.lstatSync(srcpath)\n  } catch (err) {\n    err.message = err.message.replace('lstat', 'ensureLink')\n    throw err\n  }\n\n  const dir = path.dirname(dstpath)\n  const dirExists = fs.existsSync(dir)\n  if (dirExists) return fs.linkSync(srcpath, dstpath)\n  mkdir.mkdirsSync(dir)\n\n  return fs.linkSync(srcpath, dstpath)\n}\n\nmodule.exports = {\n  createLink: u(createLink),\n  createLinkSync\n}\n", "\n\nconst u = require('universalify').fromCallback\nconst path = require('path')\nconst fs = require('graceful-fs')\nconst _mkdirs = require('../mkdirs')\nconst mkdirs = _mkdirs.mkdirs\nconst mkdirsSync = _mkdirs.mkdirsSync\n\nconst _symlinkPaths = require('./symlink-paths')\nconst symlinkPaths = _symlinkPaths.symlinkPaths\nconst symlinkPathsSync = _symlinkPaths.symlinkPathsSync\n\nconst _symlinkType = require('./symlink-type')\nconst symlinkType = _symlinkType.symlinkType\nconst symlinkTypeSync = _symlinkType.symlinkTypeSync\n\nconst pathExists = require('../path-exists').pathExists\n\nfunction createSymlink (srcpath, dstpath, type, callback) {\n  callback = (typeof type === 'function') ? type : callback\n  type = (typeof type === 'function') ? false : type\n\n  pathExists(dstpath, (err, destinationExists) => {\n    if (err) return callback(err)\n    if (destinationExists) return callback(null)\n    symlinkPaths(srcpath, dstpath, (err, relative) => {\n      if (err) return callback(err)\n      srcpath = relative.toDst\n      symlinkType(relative.toCwd, type, (err, type) => {\n        if (err) return callback(err)\n        const dir = path.dirname(dstpath)\n        pathExists(dir, (err, dirExists) => {\n          if (err) return callback(err)\n          if (dirExists) return fs.symlink(srcpath, dstpath, type, callback)\n          mkdirs(dir, err => {\n            if (err) return callback(err)\n            fs.symlink(srcpath, dstpath, type, callback)\n          })\n        })\n      })\n    })\n  })\n}\n\nfunction createSymlinkSync (srcpath, dstpath, type) {\n  const destinationExists = fs.existsSync(dstpath)\n  if (destinationExists) return undefined\n\n  const relative = symlinkPathsSync(srcpath, dstpath)\n  srcpath = relative.toDst\n  type = symlinkTypeSync(relative.toCwd, type)\n  const dir = path.dirname(dstpath)\n  const exists = fs.existsSync(dir)\n  if (exists) return fs.symlinkSync(srcpath, dstpath, type)\n  mkdirsSync(dir)\n  return fs.symlinkSync(srcpath, dstpath, type)\n}\n\nmodule.exports = {\n  createSymlink: u(createSymlink),\n  createSymlinkSync\n}\n", "\n\nconst path = require('path')\nconst fs = require('graceful-fs')\nconst pathExists = require('../path-exists').pathExists\n\n/**\n * Function that returns two types of paths, one relative to symlink, and one\n * relative to the current working directory. Checks if path is absolute or\n * relative. If the path is relative, this function checks if the path is\n * relative to symlink or relative to current working directory. This is an\n * initiative to find a smarter `srcpath` to supply when building symlinks.\n * This allows you to determine which path to use out of one of three possible\n * types of source paths. The first is an absolute path. This is detected by\n * `path.isAbsolute()`. When an absolute path is provided, it is checked to\n * see if it exists. If it does it's used, if not an error is returned\n * (callback)/ thrown (sync). The other two options for `srcpath` are a\n * relative url. By default Node's `fs.symlink` works by creating a symlink\n * using `dstpath` and expects the `srcpath` to be relative to the newly\n * created symlink. If you provide a `srcpath` that does not exist on the file\n * system it results in a broken symlink. To minimize this, the function\n * checks to see if the 'relative to symlink' source file exists, and if it\n * does it will use it. If it does not, it checks if there's a file that\n * exists that is relative to the current working directory, if does its used.\n * This preserves the expectations of the original fs.symlink spec and adds\n * the ability to pass in `relative to current working direcotry` paths.\n */\n\nfunction symlinkPaths (srcpath, dstpath, callback) {\n  if (path.isAbsolute(srcpath)) {\n    return fs.lstat(srcpath, (err) => {\n      if (err) {\n        err.message = err.message.replace('lstat', 'ensureSymlink')\n        return callback(err)\n      }\n      return callback(null, {\n        'toCwd': srcpath,\n        'toDst': srcpath\n      })\n    })\n  } else {\n    const dstdir = path.dirname(dstpath)\n    const relativeToDst = path.join(dstdir, srcpath)\n    return pathExists(relativeToDst, (err, exists) => {\n      if (err) return callback(err)\n      if (exists) {\n        return callback(null, {\n          'toCwd': relativeToDst,\n          'toDst': srcpath\n        })\n      } else {\n        return fs.lstat(srcpath, (err) => {\n          if (err) {\n            err.message = err.message.replace('lstat', 'ensureSymlink')\n            return callback(err)\n          }\n          return callback(null, {\n            'toCwd': srcpath,\n            'toDst': path.relative(dstdir, srcpath)\n          })\n        })\n      }\n    })\n  }\n}\n\nfunction symlinkPathsSync (srcpath, dstpath) {\n  let exists\n  if (path.isAbsolute(srcpath)) {\n    exists = fs.existsSync(srcpath)\n    if (!exists) throw new Error('absolute srcpath does not exist')\n    return {\n      'toCwd': srcpath,\n      'toDst': srcpath\n    }\n  } else {\n    const dstdir = path.dirname(dstpath)\n    const relativeToDst = path.join(dstdir, srcpath)\n    exists = fs.existsSync(relativeToDst)\n    if (exists) {\n      return {\n        'toCwd': relativeToDst,\n        'toDst': srcpath\n      }\n    } else {\n      exists = fs.existsSync(srcpath)\n      if (!exists) throw new Error('relative srcpath does not exist')\n      return {\n        'toCwd': srcpath,\n        'toDst': path.relative(dstdir, srcpath)\n      }\n    }\n  }\n}\n\nmodule.exports = {\n  symlinkPaths,\n  symlinkPathsSync\n}\n", "\n\nconst fs = require('graceful-fs')\n\nfunction symlinkType (srcpath, type, callback) {\n  callback = (typeof type === 'function') ? type : callback\n  type = (typeof type === 'function') ? false : type\n  if (type) return callback(null, type)\n  fs.lstat(srcpath, (err, stats) => {\n    if (err) return callback(null, 'file')\n    type = (stats && stats.isDirectory()) ? 'dir' : 'file'\n    callback(null, type)\n  })\n}\n\nfunction symlinkTypeSync (srcpath, type) {\n  let stats\n\n  if (type) return type\n  try {\n    stats = fs.lstatSync(srcpath)\n  } catch (e) {\n    return 'file'\n  }\n  return (stats && stats.isDirectory()) ? 'dir' : 'file'\n}\n\nmodule.exports = {\n  symlinkType,\n  symlinkTypeSync\n}\n", "\n\nconst u = require('universalify').fromCallback\nconst jsonFile = require('./jsonfile')\n\njsonFile.outputJson = u(require('./output-json'))\njsonFile.outputJsonSync = require('./output-json-sync')\n// aliases\njsonFile.outputJSON = jsonFile.outputJson\njsonFile.outputJSONSync = jsonFile.outputJsonSync\njsonFile.writeJSON = jsonFile.writeJson\njsonFile.writeJSONSync = jsonFile.writeJsonSync\njsonFile.readJSON = jsonFile.readJson\njsonFile.readJSONSync = jsonFile.readJsonSync\n\nmodule.exports = jsonFile\n", "\n\nconst u = require('universalify').fromCallback\nconst jsonFile = require('jsonfile')\n\nmodule.exports = {\n  // jsonfile exports\n  readJson: u(jsonFile.readFile),\n  readJsonSync: jsonFile.readFileSync,\n  writeJson: u(jsonFile.writeFile),\n  writeJsonSync: jsonFile.writeFileSync\n}\n", "\n\nconst path = require('path')\nconst mkdir = require('../mkdirs')\nconst pathExists = require('../path-exists').pathExists\nconst jsonFile = require('./jsonfile')\n\nfunction outputJson (file, data, options, callback) {\n  if (typeof options === 'function') {\n    callback = options\n    options = {}\n  }\n\n  const dir = path.dirname(file)\n\n  pathExists(dir, (err, itDoes) => {\n    if (err) return callback(err)\n    if (itDoes) return jsonFile.writeJson(file, data, options, callback)\n\n    mkdir.mkdirs(dir, err => {\n      if (err) return callback(err)\n      jsonFile.writeJson(file, data, options, callback)\n    })\n  })\n}\n\nmodule.exports = outputJson\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdir = require('../mkdirs')\nconst jsonFile = require('./jsonfile')\n\nfunction outputJsonSync (file, data, options) {\n  const dir = path.dirname(file)\n\n  if (!fs.existsSync(dir)) {\n    mkdir.mkdirsSync(dir)\n  }\n\n  jsonFile.writeJsonSync(file, data, options)\n}\n\nmodule.exports = outputJsonSync\n", "\n\nmodule.exports = {\n  moveSync: require('./move-sync')\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst copySync = require('../copy-sync').copySync\nconst removeSync = require('../remove').removeSync\nconst mkdirpSync = require('../mkdirs').mkdirpSync\nconst stat = require('../util/stat')\n\nfunction moveSync (src, dest, opts) {\n  opts = opts || {}\n  const overwrite = opts.overwrite || opts.clobber || false\n\n  const { srcStat } = stat.checkPathsSync(src, dest, 'move')\n  stat.checkParentPathsSync(src, srcStat, dest, 'move')\n  mkdirpSync(path.dirname(dest))\n  return doRename(src, dest, overwrite)\n}\n\nfunction doRename (src, dest, overwrite) {\n  if (overwrite) {\n    removeSync(dest)\n    return rename(src, dest, overwrite)\n  }\n  if (fs.existsSync(dest)) throw new Error('dest already exists.')\n  return rename(src, dest, overwrite)\n}\n\nfunction rename (src, dest, overwrite) {\n  try {\n    fs.renameSync(src, dest)\n  } catch (err) {\n    if (err.code !== 'EXDEV') throw err\n    return moveAcrossDevice(src, dest, overwrite)\n  }\n}\n\nfunction moveAcrossDevice (src, dest, overwrite) {\n  const opts = {\n    overwrite,\n    errorOnExist: true\n  }\n  copySync(src, dest, opts)\n  return removeSync(src)\n}\n\nmodule.exports = moveSync\n", "\n\nconst u = require('universalify').fromCallback\nmodule.exports = {\n  move: u(require('./move'))\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst copy = require('../copy').copy\nconst remove = require('../remove').remove\nconst mkdirp = require('../mkdirs').mkdirp\nconst pathExists = require('../path-exists').pathExists\nconst stat = require('../util/stat')\n\nfunction move (src, dest, opts, cb) {\n  if (typeof opts === 'function') {\n    cb = opts\n    opts = {}\n  }\n\n  const overwrite = opts.overwrite || opts.clobber || false\n\n  stat.checkPaths(src, dest, 'move', (err, stats) => {\n    if (err) return cb(err)\n    const { srcStat } = stats\n    stat.checkParentPaths(src, srcStat, dest, 'move', err => {\n      if (err) return cb(err)\n      mkdirp(path.dirname(dest), err => {\n        if (err) return cb(err)\n        return doRename(src, dest, overwrite, cb)\n      })\n    })\n  })\n}\n\nfunction doRename (src, dest, overwrite, cb) {\n  if (overwrite) {\n    return remove(dest, err => {\n      if (err) return cb(err)\n      return rename(src, dest, overwrite, cb)\n    })\n  }\n  pathExists(dest, (err, destExists) => {\n    if (err) return cb(err)\n    if (destExists) return cb(new Error('dest already exists.'))\n    return rename(src, dest, overwrite, cb)\n  })\n}\n\nfunction rename (src, dest, overwrite, cb) {\n  fs.rename(src, dest, err => {\n    if (!err) return cb()\n    if (err.code !== 'EXDEV') return cb(err)\n    return moveAcrossDevice(src, dest, overwrite, cb)\n  })\n}\n\nfunction moveAcrossDevice (src, dest, overwrite, cb) {\n  const opts = {\n    overwrite,\n    errorOnExist: true\n  }\n  copy(src, dest, opts, err => {\n    if (err) return cb(err)\n    return remove(src, cb)\n  })\n}\n\nmodule.exports = move\n", "\n\nconst u = require('universalify').fromCallback\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdir = require('../mkdirs')\nconst pathExists = require('../path-exists').pathExists\n\nfunction outputFile (file, data, encoding, callback) {\n  if (typeof encoding === 'function') {\n    callback = encoding\n    encoding = 'utf8'\n  }\n\n  const dir = path.dirname(file)\n  pathExists(dir, (err, itDoes) => {\n    if (err) return callback(err)\n    if (itDoes) return fs.writeFile(file, data, encoding, callback)\n\n    mkdir.mkdirs(dir, err => {\n      if (err) return callback(err)\n\n      fs.writeFile(file, data, encoding, callback)\n    })\n  })\n}\n\nfunction outputFileSync (file, ...args) {\n  const dir = path.dirname(file)\n  if (fs.existsSync(dir)) {\n    return fs.writeFileSync(file, ...args)\n  }\n  mkdir.mkdirsSync(dir)\n  fs.writeFileSync(file, ...args)\n}\n\nmodule.exports = {\n  outputFile: u(outputFile),\n  outputFileSync\n}\n"]}