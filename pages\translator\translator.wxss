/* pages/translator/translator.wxss */
.change-mode {
  position: absolute;
  bottom: 0;
  left: 20rpx;
}

.upload-image-button {
  width: 80rpx !important;
  height: 80rpx;
  border-radius: 50%;
  border: 2rpx solid #ccc;
  background-color: #fff;
  padding: 0;
  line-height: 60rpx;
  text-align: center;
  font-size: 40rpx;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.chat-messages {
  flex: 1;
  padding: 10px;
  background-color: #f0f0f0;
  height: 600rpx;
}

.message {
  max-width: 80%;
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 10px;
  word-wrap: break-word;
}

.user {
  align-self: flex-end;
  background-color: #dcf8c6;
  margin-left: auto;
}

.assistant {
  align-self: flex-start;
  background-color: #ffffff;
}

.input-area {
  display: flex;
  padding: 10px;
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
  position: relative;
}

.input-area.wrap {
  flex-wrap: wrap;
}

.input-area.translate {
  height: 240rpx;
  justify-content: center;
  align-items: center;
}

.message-input {
  flex: 1;
  height: 40px;
  padding: 0 10px;
  border: 1px solid #ccc;
  border-radius: 20px;
  margin-right: 10px;
}

.send-button {
  width: 80px !important;
  height: 40px;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 16px;
  line-height: 40px;
  padding: 0;
}

.send-button[disabled] {
  background-color: #cccccc;
}

.clear-image {
  position: absolute;
  top: 20rpx;
  transform: translateX(-110%);
  left: 0rpx;
  border-radius: 50%;
  background-color: #000;
  opacity: 0.5;
  text-align: center;
  line-height: 40rpx;
  height: 40rpx;
  width: 40rpx;
  color: white;
}

.upload-image-view {
  width: 200rpx;
  height: 200rpx;
  position: absolute;
  top: 0;
  right: 20rpx;
  transform: translateY(-110%);
}

.upload-image-view__image {
  width: 100%;
  height: 100%;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  background-color: #f0f0f0;
}

.loading-gif {
  width: 40px;
  height: 40px;
}

.voice-button {
  width: 50px;
  height: 50px;
  /* background-color: #007AFF; */
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  line-height: 50px;
  padding: 0;
  margin-left: 10px;
}

.recording-indicator {
  position: fixed;
  bottom: 160px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 50px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 25px;
  font-size: 18px;
  z-index: 100;
}

.light-list {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  transform: translateY(-100%);
  background-color: #fff;
}

.light-list__title {
  text-align: center;
  padding: 10rpx 0;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #ccc;
}

.light-list__title__text {
  margin: auto;
}

.light-list__title__add {
  background-color: #007AFF;
  color: #fff;
  border-radius: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  margin-right: 10rpx;
  padding: 10rpx 40rpx;
  position: absolute;
  right: 10rpx;
}

.light-list__text {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
  padding: 0 10rpx;
  padding-top: 20rpx;
}

.light-list__text__item {
  font-size: 24rpx;
  display: inline-block;
  border: 1rpx solid #007AFF;
  border-radius: 20rpx;
  padding: 4rpx 8rpx;
  position: relative;
}

.light-list__text__item::after {
  content: 'x';
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
  width: 26rpx;
  height: 26rpx;
  font-size: 18rpx;
  line-height: 26rpx;
  border-radius: 50%;
  background-color: #111;
  color: #fff;
  display: block;
  text-align: center;
}



.voice-button[disabled] {
  background-color: #cccccc;
}

.button-box {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 100rpx;
  margin-left: 120rpx;
}

.translate-box {
  /* height: 1000rpx; */
  flex: 1;
}

.translate-item-top,
.translate-item-bottom {
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
  height: 600rpx;
  padding-top: 20rpx;
}

.translate-item-top {
  height: 1200rpx;
  /* border-bottom: 2rpx solid #007AFF; */
}

.bottomarea-right-top {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
}

.export-button {
  display: block;
  font-size: 24rpx;
  border-radius: 36rpx;
}

.clear-button {
  font-size: 24rpx;
  border-radius: 36rpx;
  margin-top: 8rpx;
  background-color: #007AFF;
  color: #fff;
}

.status-title {
  font-size: 28rpx;
  font-weight: bolder;
  white-space: nowrap;
  text-align: center;
}

.status-text {
  text-align: center;
  font-size: 28rpx;
  color: red;
}

.status-text.green {
  color: rgb(23, 243, 115)
}


.translate-item-header {
  text-align: center;
  /* border-bottom: 2rpx solid #ccc; */
  padding-top: 20rpx;
}

.translate-content-box {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  overflow: auto;
  height: 100%;
  width: 62%;
}

.translate-content-outter {
  display: flex;
  height: 100%;
}

.languages {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0 12rpx;
  gap: 6rpx;
}

.languages-box {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0 12rpx;
  gap: 6rpx;
  max-height: 640rpx;
  overflow: auto;
}

.languages-header {
  font-size: 28rpx;
  text-align: center;
  font-weight: 700;
}

.language-item {
  border-radius: 24rpx;
  border: 2rpx solid #ccc;
  padding: 8rpx;
  text-align: center;
  font-size: 24rpx;
  flex-shrink: 0;
  white-space: nowrap;
}

.language-item.active {
  background-color: #007AFF;
  color: #fff;
}

.translate-content-self {
  text-align: right;
  border-radius: 24rpx 0 24rpx 24rpx;
  background-color: #007AFF;
  color: #fff;
  width: fit-content;
  font-size: 28rpx;
  padding: 14rpx;
  margin: 0 14rpx 0 auto;
}

.translate-content-other {
  border-radius: 0 24rpx 24rpx 24rpx;
  background-color: #fff;
  width: fit-content;
  padding: 14rpx;
  margin-left: 14rpx;
  font-size: 28rpx;
}

.tts-enable {
  display: flex;
  align-items: center;
  position: absolute;
  left: 20rpx;
  top: 20rpx;
  gap: 10rpx;
  font-size: 28rpx;
  color: #222;
}

.system-response {
  background-color: #666;
  padding: 16rpx;
  border-radius: 32rpx;
}

.system-response_loading {
  width: 60rpx;
  height: 60rpx;
  display: block;
  margin: auto;
  margin-top: 30rpx;
}

.system-response_title {
  font-weight: bolder;
  font-size: 36rpx;
  color: #fff;
}

.system-response_content {
  font-size: 28rpx;
  color: #333;
  color: #fff;
}

.system-response_image {
  width: 100%;
  margin-top: 32rpx;
  /* height: auto; */
}

.translate-type-switch {
  position: absolute;
  top: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 24rpx;
  background-color: #fff;
  color: #007AFF;
  /* padding: 0; */
}


.voice-button-svg {
  height: 100%;
  width: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  background-image: url("data:image/svg+xml;base64,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");
}