export const kedaRange = [{
    lang: 'zh',
    langText: '中文'
  },
  {
    lang: 'ur',
    langText: '乌尔都语'
  },
  {
    lang: 'en',
    langText: '英文'
  },
  {
    lang: 'bn',
    langText: '孟加拉语'
  },
  {
    lang: 'ja',
    langText: '日语'
  },
  {
    lang: 'ta',
    langText: '泰米尔语'
  },
  {
    lang: 'ko',
    langText: '韩语'
  },
  {
    lang: 'uk',
    langText: '乌克兰语'
  },
  {
    lang: 'ru',
    langText: '俄语'
  },
  {
    lang: 'kk',
    langText: '哈萨克语'
  },
  {
    lang: 'fr',
    langText: '法语'
  },
  {
    lang: 'uz',
    langText: '乌兹别克语'
  },
  {
    lang: 'es',
    langText: '西班牙语'
  },
  {
    lang: 'pl',
    langText: '波兰语'
  },
  {
    lang: 'ar',
    langText: '阿拉伯语'
  },
  {
    lang: 'mn',
    langText: '蒙语'
  },
  {
    lang: 'de',
    langText: '德语'
  },
  {
    lang: 'sw',
    langText: '斯瓦西里语'
  },
  {
    lang: 'th',
    langText: '泰语'
  },
  {
    lang: 'ha',
    langText: '豪撒语'
  },
  {
    lang: 'vi',
    langText: '越南语'
  },
  {
    lang: 'fa',
    langText: '波斯语'
  },
  {
    lang: 'hi',
    langText: '印地语'
  },
  {
    lang: 'nl',
    langText: '荷兰语'
  },
  {
    lang: 'pt',
    langText: '葡萄牙语'
  },
  {
    lang: 'sv',
    langText: '瑞典语'
  },
  {
    lang: 'it',
    langText: '意大利语'
  },
  {
    lang: 'ro',
    langText: '罗马尼亚语'
  },
  {
    lang: 'ms',
    langText: '马来语'
  },
  {
    lang: 'bg',
    langText: '保加利亚语'
  },
  {
    lang: 'id',
    langText: '印尼语'
  },
  {
    lang: 'fil',
    langText: '菲律宾语'
  },
  {
    lang: 'tib',
    langText: '藏语'
  },
  {
    lang: 'tr',
    langText: '土耳其语'
  },
  {
    lang: 'el',
    langText: '希腊语'
  },
  {
    lang: 'cs',
    langText: '捷克语'
  },
  {
    lang: 'ug',
    langText: '维吾尔语'
  }
];

export const weiruanRange = [{
    lang: 'zh',
    langText: '中文'
  },
  {
    lang: 'ur',
    langText: '乌尔都语'
  },
  {
    lang: 'en',
    langText: '英文'
  },
  {
    lang: 'bn',
    langText: '孟加拉语'
  },
  {
    lang: 'ja',
    langText: '日语'
  },
  {
    lang: 'ta',
    langText: '泰米尔语'
  },
  {
    lang: 'ko',
    langText: '韩语'
  },
  {
    lang: 'uk',
    langText: '乌克兰语'
  },
  {
    lang: 'ru',
    langText: '俄语'
  },
  {
    lang: 'kk',
    langText: '哈萨克语'
  },
  {
    lang: 'fr',
    langText: '法语'
  },
  {
    lang: 'uz',
    langText: '乌兹别克语'
  },
  {
    lang: 'es',
    langText: '西班牙语'
  },
  {
    lang: 'pl',
    langText: '波兰语'
  },
  {
    lang: 'ar',
    langText: '阿拉伯语'
  },
  {
    lang: 'mn',
    langText: '蒙语'
  },
  {
    lang: 'de',
    langText: '德语'
  },
  {
    lang: 'sw',
    langText: '斯瓦西里语'
  },
  {
    lang: 'th',
    langText: '泰语'
  },
  {
    lang: 'vi',
    langText: '越南语'
  },
  {
    lang: 'fa',
    langText: '波斯语'
  },
  {
    lang: 'hi',
    langText: '印地语'
  },
  {
    lang: 'nl',
    langText: '荷兰语'
  },
  {
    lang: 'pt',
    langText: '葡萄牙语'
  },
  {
    lang: 'sv',
    langText: '瑞典语'
  },
  {
    lang: 'it',
    langText: '意大利语'
  },
  {
    lang: 'ro',
    langText: '罗马尼亚语'
  },
  {
    lang: 'ms',
    langText: '马来语'
  },
  {
    lang: 'bg',
    langText: '保加利亚语'
  },
  {
    lang: 'id',
    langText: '印尼语'
  },
  {
    lang: 'fil',
    langText: '菲律宾语'
  },
  {
    lang: 'tr',
    langText: '土耳其语'
  },
  {
    lang: 'el',
    langText: '希腊语'
  },
  {
    lang: 'cs',
    langText: '捷克语'
  }
];

export const awsRange = [{
    lang: 'zh',
    langText: '中文'
  },
  {
    lang: 'en',
    langText: '英文'
  },
  {
    lang: 'ja',
    langText: '日语'
  },
  {
    lang: 'ko',
    langText: '韩语'
  },
  {
    lang: 'uk',
    langText: '乌克兰语'
  },
  {
    lang: 'fr',
    langText: '法语'
  },
  {
    lang: 'es',
    langText: '西班牙语'
  },
  {
    lang: 'ru',
    langText: '俄语'
  },
  {
    lang: 'pl',
    langText: '波兰语'
  },
  {
    lang: 'de',
    langText: '德语'
  },
  {
    lang: 'th',
    langText: '泰语'
  },
  {
    lang: 'vi',
    langText: '越南语'
  },
  {
    lang: 'ar',
    langText: '阿拉伯语'
  },
  {
    lang: 'hi',
    langText: '印地语'
  },
  {
    lang: 'nl',
    langText: '荷兰语'
  },
  {
    lang: 'pt',
    langText: '葡萄牙语'
  },
  {
    lang: 'sv',
    langText: '瑞典语'
  },
  {
    lang: 'it',
    langText: '意大利语'
  },
  {
    lang: 'ro',
    langText: '罗马尼亚语'
  },
  {
    lang: 'ms',
    langText: '马来语'
  },
  {
    lang: 'id',
    langText: '印尼语'
  },
  {
    lang: 'fil',
    langText: '菲律宾语'
  },
  {
    lang: 'cs',
    langText: '捷克语'
  },
];
export const aliRange = [{
  lang: 'zh',
  langText: '中文'
},
{
  lang: 'ur',
  langText: '乌尔都语'
},
{
  lang: 'en',
  langText: '英文'
},
{
  lang: 'bn',
  langText: '孟加拉语'
},
{
  lang: 'ja',
  langText: '日语'
},
{
  lang: 'ta',
  langText: '泰米尔语'
},
{
  lang: 'ko',
  langText: '韩语'
},
{
  lang: 'uk',
  langText: '乌克兰语'
},
{
  lang: 'ru',
  langText: '俄语'
},
{
  lang: 'kk',
  langText: '哈萨克语'
},
{
  lang: 'fr',
  langText: '法语'
},
{
  lang: 'uz',
  langText: '乌兹别克语'
},
{
  lang: 'es',
  langText: '西班牙语'
},
{
  lang: 'pl',
  langText: '波兰语'
},
{
  lang: 'ar',
  langText: '阿拉伯语'
},
{
  lang: 'mn',
  langText: '蒙语'
},
{
  lang: 'de',
  langText: '德语'
},
{
  lang: 'sw',
  langText: '斯瓦西里语'
},
{
  lang: 'th',
  langText: '泰语'
},
{
  lang: 'ha',
  langText: '豪撒语'
},
{
  lang: 'vi',
  langText: '越南语'
},
{
  lang: 'fa',
  langText: '波斯语'
},
{
  lang: 'hi',
  langText: '印地语'
},
{
  lang: 'nl',
  langText: '荷兰语'
},
{
  lang: 'pt',
  langText: '葡萄牙语'
},
{
  lang: 'sv',
  langText: '瑞典语'
},
{
  lang: 'it',
  langText: '意大利语'
},
{
  lang: 'ro',
  langText: '罗马尼亚语'
},
{
  lang: 'ms',
  langText: '马来语'
},
{
  lang: 'bg',
  langText: '保加利亚语'
},
{
  lang: 'id',
  langText: '印尼语'
},
{
  lang: 'fil',
  langText: '菲律宾语'
},
{
  lang: 'tr',
  langText: '土耳其语'
},
{
  lang: 'el',
  langText: '希腊语'
},
{
  lang: 'cs',
  langText: '捷克语'
},
{
  lang: 'ug',
  langText: '维吾尔语'
}
];

export const serverRange = [
  { lang: 'zh',  langText: '中文' },
  { lang: 'en',  langText: '英文' },
  { lang: 'ja',  langText: '日语' },
  { lang: 'ko',  langText: '韩语' },
  { lang: 'ru',  langText: '俄语' },
  { lang: 'fr',  langText: '法语' },
  { lang: 'es',  langText: '西班牙语' },
  { lang: 'ar',  langText: '阿拉伯语' },
  { lang: 'de',  langText: '德语' },
  { lang: 'th',  langText: '泰语' },
  { lang: 'vi',  langText: '越南语' },
  { lang: 'hi',  langText: '印地语' },
  { lang: 'pt',  langText: '葡萄牙语' },
  { lang: 'it',  langText: '意大利语' },
  { lang: 'ms',  langText: '马来语' },
  { lang: 'id',  langText: '印尼语' },
  { lang: 'fil', langText: '菲律宾语' },
  { lang: 'tr',  langText: '土耳其语' },
  { lang: 'el',  langText: '希腊语' },
  { lang: 'cs',  langText: '捷克语' },
  { lang: 'ur',  langText: '乌尔都语' },
  { lang: 'bn',  langText: '孟加拉语' },
  { lang: 'ta',  langText: '泰米尔语' },
  { lang: 'uk',  langText: '乌克兰语' },
  { lang: 'kk',  langText: '哈萨克语' },
  { lang: 'uz',  langText: '乌兹别克语' },
  { lang: 'pl',  langText: '波兰语' },
  { lang: 'mn',  langText: '蒙古语（外蒙）' },
  { lang: 'sw',  langText: '斯瓦希里语' },
  { lang: 'ha',  langText: '豪萨语' },
  { lang: 'fa',  langText: '波斯语' },
  { lang: 'nl',  langText: '荷兰语' },
  { lang: 'sv',  langText: '瑞典语' },
  { lang: 'ro',  langText: '罗马尼亚语' },
  { lang: 'bg',  langText: '保加利亚语' },
  { lang: 'ug',  langText: '维语' },
  { lang: 'tib', langText: '藏语' },      // 科大ASR大模型独有
  { lang: 'my',  langText: '缅甸语' },    // 阿里云ASR独有
  { lang: 'lo',  langText: '老挝语' },    // 阿里云ASR独有
  { lang: 'km',  langText: '高棉语' },    // 阿里云ASR独有
  { lang: 'ne',  langText: '尼泊尔语' },  // 阿里云ASR独有
  { lang: 'si',  langText: '僧伽罗语' },  // 阿里云ASR独有
  { lang: 'he',  langText: '希伯来语' },  // 阿里云ASR独有
  { lang: 'te',  langText: '泰卢固语' },  // 阿里云ASR独有
  { lang: 'mr',  langText: '马拉地语' },  // 阿里云ASR独有
  { lang: 'pa',  langText: '旁遮普语' },  // 阿里云ASR独有
  { lang: 'jv',  langText: '爪哇语' },    // 阿里云ASR独有
  { lang: 'hu',  langText: '匈牙利语' },  // 阿里云ASR独有
  { lang: 'ca',  langText: '加泰罗尼亚语' }, // 阿里云ASR独有
  { lang: 'hr',  langText: '克罗地亚语' },   // 阿里云ASR独有
  { lang: 'dA',  langText: '丹麦语' },       // 阿里云ASR独有（若服务端是 'da'，请改回小写）
  { lang: 'no',  langText: '挪威语' },       // 阿里云ASR独有
  { lang: 'az',  langText: '阿塞拜疆语' },   // 阿里云ASR独有
  { lang: 'kn',  langText: '坎纳达语' },     // 阿里云ASR独有
  { lang: 'ml',  langText: '马拉雅拉姆语' }  // 阿里云ASR独有
];