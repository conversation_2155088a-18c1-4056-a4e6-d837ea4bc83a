App({
  onLaunch() {
    const sysInfo = wx.getSystemInfoSync();

    const getBottomHeight = () => {
      const _sysInfo = wx.getSystemInfoSync();
      const bottomHeight = (_sysInfo.windowHeight - _sysInfo.safeArea.bottom) * pixelRatio
      const contentHeight = (_sysInfo.safeArea.bottom - statusHeightPx - navHeightPx) * pixelRatio
      const contentWithoutBottomBarHeight = contentHeight - 98 - (bottomHeight === 0 ? 10 : 0)
      return {
        bottomHeight,
        contentWithoutBottomBarHeight,
        contentHeight
      }
    }

    const pixelRatio = 750 / sysInfo.windowWidth;
    const navHeightPx = sysInfo.statusBarHeight;
    const windowHeight = sysInfo.safeArea.bottom * pixelRatio
    const {
      bottomHeight,
      contentWithoutBottomBarHeight,
      contentHeight
    } = getBottomHeight()
    const statusHeightPx = sysInfo.statusBarHeight * pixelRatio;
    const navHeight = navHeightPx * pixelRatio;
    this.globalData = {
      pixelRatio,
      navHeight,
      statusHeight: statusHeightPx,
      windowHeight,
      bottomHeight,
      contentWithoutBottomBarHeight,
      contentHeight
    }
  },
  globalData: {
  }
})