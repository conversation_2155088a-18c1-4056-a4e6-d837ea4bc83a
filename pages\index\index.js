import mpImageCompress from 'mp-image-compress'
Page({

  data: {

  },


  onLoad(options) {

  },

  onReady() {

  },

  onShow() {

  },

  onHide() {

  },

  onUnload() {

  },
  startRecognizeThings() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sizeType: 'compressed',
      success: res => {
        let temp = res.tempFiles[0].tempFilePath
        wx.cropImage({
          src: temp,
          cropScale: "1:1",
          success: async r => {
            let tempCrop = r.tempFilePath
            const imgRes = await mpImageCompress.set(tempCrop, 125) // 1024K
            console.log(imgRes)
            wx.navigateTo({
              url: '/pages/imageChater/imageChater?imageUrl=' + imgRes.filePath,
            })
          },
          fail: err => {
            console.log(err)
            wx.showToast({
              title: '拍照失败',
              icon: 'error'
            })
          }
        })
      },
      fail: err => {
        console.log(err)
      }
    })
  },
  startRecognizeSolve() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sizeType: 'compressed',
      success: res => {
        let temp = res.tempFiles[0].tempFilePath
        wx.cropImage({
          src: temp,
          cropScale: "1:1",
          success: async r => {
            let tempCrop = r.tempFilePath
            const imgRes = await mpImageCompress.set(tempCrop, 125) // 1024K
            console.log(imgRes)
            wx.navigateTo({
              url: '/pages/imageSolver/imageSolver?imageUrl=' + imgRes.filePath,
            })
          },
          fail: err => {
            console.log(err)
            wx.showToast({
              title: '拍照失败',
              icon: 'error'
            })
          }
        })
      },
      fail: err => {
        console.log(err)
      }
    })
  }
})