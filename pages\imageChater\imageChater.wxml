<!--pages/imageChater/imageChater.wxml-->
<view class="page">
  <status-bar title="拍照识物" background="#fff"></status-bar>

  <view class="content">

    <view class="image-box">
      <image class="image" src="{{imageUrl}}" mode="aspectFit" />
    </view>

    <view class="title-text">
      <text>{{titleText}}</text>
    </view>

    <view wx:if="{{isStreaming}}" style="display: flex; justify-content: center;margin-top: 24rpx;">
      <image class="loading-image" src="/static/pics/loading.gif" mode="" />
      <view>正在{{recogType === 0 ? '识别图片' : recogType === 1 ? '搜集百科中' : '生成故事'}}中...</view>
    </view>

    <view class="operations">
      <button disabled="{{isStreaming}}" class="button-bottom" bind:tap="baike">百科回答</button>
      <button disabled="{{isStreaming}}" class="button-bottom green" bind:tap="story">生成故事</button>
      <button disabled="{{isStreaming}}" class="button-bottom gray" bind:tap="shot">继续拍照</button>

    </view>

  </view>

</view>