<!--pages/translator/translator.wxml-->
<wxs module="computedText">
  module.exports = {
  getPickerName: function (apiMode) {
  if (apiMode === 0) return 'gpt-4o(不可用) '
  else if (apiMode === 1) return '星火智能体-小蓝'
  else if (apiMode === 2) return '智能体-老人陪聊'
  else if (apiMode === 3) return '开关灯'
  else if (apiMode === 4) return '翻译'
  else if (apiMode === 5) return '涂鸦测试'
  else if (apiMode === 6) return '豆包'
  else if (apiMode === 7) return '翻译'
  else if (apiMode === 8) return '豆包翻译'
  else if (apiMode === 9) return '谷歌翻译'
  else if (apiMode === 10) return '1对1翻译'
  }
  }
</wxs>
<view class="chat-container">
  <status-bar title="平台ASR测试"></status-bar>
  <block wx:if="{{apiMode !== 7 && apiMode !== 8 && apiMode !== 9 && apiMode !== 10}}">
    <scroll-view scroll-y class="chat-messages" scroll-into-view="{{scrollToView}}">
      <block wx:for="{{messages}}" wx:key="index">
        <view id="msg-{{index}}" class="message {{item.role === 'user' ? 'user' : 'assistant'}}" bind:tap="onTapMessage"
          data-content="{{item.content}}" data-item="{{item}}">
          <text user-select>{{item.content}}</text>
          <image bind:tap="onTapImage" wx:if="{{item.imageSrc}}" src="{{item.imageSrc}}" mode="aspectFit"
            data-src="{{item.imageSrc}}" />
        </view>
      </block>
      <!-- 显示loading GIF -->
      <view wx:if="{{isStreaming}}" class="loading-container">
        <image class="loading-gif" src="/static/pics/loading.gif"></image>
      </view>
      <view id="bottom-anchor" style="height: {{apiMode === 3 ? '60' : apiMode === 6 ? '130' : '0'}}px;"></view>
    </scroll-view>
  </block>


  <!-- 谷歌翻译区域 -->
  <block wx:if="{{apiMode === 7 || apiMode === 8 || apiMode === 9 || apiMode === 10}}">
    <view class="translate-box">
      <view class="translate-item-top">
        <!-- <view class="translate-item-header">游客屏幕</view> -->
        <view class="translate-content-outter">
          <view class="languages">
            <block wx:if="{{apiMode === 7}}">
              <!-- <view class="languages-header">翻译语言</view>
              <view class="languages-box">
                <view wx:for="{{languagesRange}}" wx:key="lang"
                  class="language-item {{user2Language === item.lang ? 'active' : ''}}" bind:tap="switchUser2Language"
                  data-lang="{{item.lang}}">{{item.langText}}</view>
              </view> -->

            </block>

            <!-- <view class="languages-header" style="margin-top: 32rpx;">翻译模型</view> -->
            <!-- <view wx:for="{{translateModels}}" wx:key="lang"
              class="language-item {{selectedTranslateModel === item.id ? 'active' : ''}}"
              bind:tap="switchTranslateModel" data-id="{{item.id}}">{{item.name}}</view> -->

            <block wx:if="{{false}}">
              <view class="languages-header" style="margin-top: 32rpx;">TTS模型</view>
              <view wx:for="{{ttsModels}}" wx:key="lang"
                class="language-item {{selectedTtsModel === item.id ? 'active' : ''}}" bind:tap="switchTtsModel"
                data-id="{{item.id}}">{{item.name}}</view>
            </block>


            <block wx:if="{{apiMode === 10}}">
              <view class="status-title">翻译服务器</view>
              <view class="status-text {{asrServerConnected ? 'green' : ''}}">{{asrServerConnected ? '已连接' : '未连接'}}
              </view>

              <view class="status-title">对方状态</view>
              <view class="status-text {{otherSideIsConnected ? 'green' : ''}}">{{otherSideIsConnected ? '已加入' : '未加入'}}
              </view>
            </block>

          </view>

          <view class="translate-content-box">
            <block wx:for="{{translateUser1Messages}}" wx:key="index">
              <block wx:if="{{item.user != 'system'}}">
                <view data-item="{{item}}" bind:tap="onTapContent"
                  class="translate-content-{{item.user==='user1' ? 'self' : 'other'}}">
                  {{item.message}}
                </view>
              </block>
              <block wx:elif="{{item.user==='system'}}">
                <view class="system-response">
                  <view class="system-response_title">系统回复</view>
                  <block>
                    <view class="system-response_content"><text>{{item.message}}</text></view>
                    <block wx:for="{{item.imageUrls}}" wx:for-item="imageUrl">
                      <image data-src="{{imageUrl}}" bind:tap="onTapSystemImage" class="system-response_image"
                        src="{{imageUrl}}" mode="widthFix" />
                    </block>

                  </block>
                  <block wx:if="{{!item.isFinished}}">
                    <image class="system-response_loading" src="/static/pics/loading.gif" mode="" />
                  </block>
                </view>
              </block>
            </block>
          </view>
          <view class="languages">
            <view class="languages-header">{{apiMode === 7 ? 'ASR语言' : "您的语言"}}</view>
            <view class="languages-box">
              <view wx:for="{{languagesRange}}" wx:key="lang"
                class="language-item {{user1Language === item.lang ? 'active' : ''}}" bind:tap="switchUser1Language"
                data-lang="{{item.lang}}">{{item.langText}}</view>
            </view>

            <!-- <view class="languages-header" style="margin-top: 32rpx;">ASR模型</view>
            <view wx:for="{{asrModels}}" wx:key="index" data-id="{{item.id}}"
              class="language-item {{selectedAsrModel === item.id ? 'active' :''}}" bind:tap="switchAsrModel">
              {{item.name}}</view> -->
          </view>
        </view>
      </view>
      <view wx:if="{{false}}" class="translate-item-bottom">
        <view class="translate-item-header">客服屏幕</view>
        <view class="translate-content-outter">
          <view class="translate-content-box">
            <block wx:for="{{translateUser2Messages}}" wx:key="index">
              <view class="translate-content-{{item.user==='user2' ? 'self' : 'other'}}">
                {{item.message}}
              </view>
            </block>
          </view>
          <view class="languages">
            <view wx:for="{{languagesRange}}" wx:key="lang"
              class="language-item {{user2Language === item.lang ? 'active' : ''}}" bind:tap="switchUser2Language"
              data-lang="{{item.lang}}">{{item.langText}}</view>
          </view>
        </view>
      </view>

      <!-- <view class="translate-item-left">
              <view class="translate-item-header">翻译内容</view>
              <view class="translate-content"> <text>{{translateText}}</text></view>
          </view>
          <view class="translate-item-right">
              <view class="translate-item-header">用户输入</view>
              <view class="translate-content"> <text>{{translateUserText}}</text></view>
          </view> -->
    </view>

    <view class="input-area translate">
      <button data-type="{{0}}" bind:tap="switchGoogleTranslate" type="primary" style="border-radius: 42rpx;"
        loading="{{isLoadingTranslate}}">{{isTranslating ? '点击停止' :'点击开始说话'}}</button>
      <!-- <button data-type="{{1}}" bind:tap="switchGoogleTranslate"
              type="button">{{isTranslating ? '停止翻译' :'客服说话'}}</button> -->

      <view class="bottomarea-right-top">
        <button class="export-button" bind:tap="exportRecord" open-type="share">复制翻译记录</button>

        <button class="clear-button" bind:tap="clearMessages">清除消息列表</button>
      </view>


      <button wx:if="{{false}}" class="translate-type-switch" type="button"
        bind:tap="switchTranslateSpeakMode">{{apiMode === 7 ? '切换到1对1翻译' : '切换到普通翻译'}}</button>

      <view wx:if="{{false}}" class="tts-enable">
        <text>开启TTS</text>
        <switch checked="{{isTts}}" bindchange="onTtsEnableChange" />
      </view>

    </view>
  </block>


  <!-- Voice Input Button -->
  <view wx:if="{{apiMode!==0 && apiMode !== 7 && apiMode !== 8 && apiMode !== 9 && apiMode !== 10}}"
    class="input-area {{apiMode === 4 ? 'wrap' : ''}}">
    <view class="upload-image-view" wx:if="{{imageSrc}}">
      <image class="upload-image-view__image" src="{{imageSrc}}" mode="aspectFit" />
      <view class="clear-image" bind:tap="clearImage">x</view>
    </view>

    <view class="light-list">
      <!-- 设备列表 -->
      <view wx:if="{{apiMode === 3 || apiMode === 6}}">
        <view class="light-list__title" bind:tap="onTapAddLight">
          <view class="light-list__title__text">设备列表</view>
          <view class="light-list__title__add">添加设备</view>
        </view>
        <view class="light-list__text">
          <block wx:if="{{lightList.length === 0}}">
            <view style="text-align: center;font-size: 24rpx;color: #888;margin: auto;">您暂未添加任何灯</view>
          </block>
          <block wx:else>
            <view wx:for="{{lightList}}" wx:key="index" class="light-list__text__item" data-item="{{item}}"
              bind:tap="onTapLightListItem">
              {{item.name}}
            </view>
          </block>
        </view>
      </view>
      <!-- 人员列表 -->
      <view style="margin-top: 10rpx;" wx:if="{{apiMode === 6}}">
        <view class="light-list__title">
          <view class="light-list__title__text">呼叫列表</view>
          <view class="light-list__title__add" bind:tap="onTapAddPerson">添加人员</view>
        </view>
        <view class="light-list__text">
          <block wx:if="{{personList.length === 0}}">
            <view style="text-align: center;font-size: 24rpx;color: #888;margin: auto;">您暂未添加任何人员</view>
          </block>
          <block wx:else>
            <view wx:for="{{personList}}" wx:key="index" class="light-list__text__item" data-item="{{item}}"
              bind:tap="onTapPersonListItem">
              {{item.name}}
            </view>
          </block>
        </view>
      </view>
    </view>

    <input class="message-input" type="text" placeholder="输入消息..." value="{{inputMessage}}" bindinput="onInputChange"
      disabled="{{isStreaming}}" wx:if="{{apiMode !== 4}}" />
    <block wx:if="{{apiMode === 4}}">
      <view data-lang="0" class="button-box" bindtouchstart="startRecording" bindtouchend="stopRecording"
        bind:touchcancel="onTouchCancel">
        <text>to Kor</text>
        <button class="voice-button">
          <view class="voice-button-svg"></view>
        </button>
      </view>

      <view data-lang="1" class="button-box" bindtouchstart="startRecording" bindtouchend="stopRecording"
        bind:touchcancel="onTouchCancel">
        <text>to Chs</text>
        <button class="voice-button">
          <view class="voice-button-svg"></view>
        </button>
      </view>

      <view data-lang="2" class="button-box" bindtouchstart="startRecording" bindtouchend="stopRecording"
        bind:touchcancel="onTouchCancel">
        <text>to Kyr</text>
        <button class="voice-button">
          <view class="voice-button-svg"></view>
        </button>
      </view>

      <view data-lang="3" class="button-box" bindtouchstart="startRecording" bindtouchend="stopRecording"
        bind:touchcancel="onTouchCancel">
        <text>to Eng</text>
        <button class="voice-button">
          <view class="voice-button-svg"></view>
        </button>
      </view>
    </block>
    <block wx:else>
      <button wx:if="{{apiMode === 6}}" class="voice-button" bind:tap="switchSpeaking">
        <view class="voice-button-svg"></view>
      </button>
      <button wx:else class="voice-button" bindtouchstart="startRecording" bindtouchend="stopRecording"
        bind:touchcancel="onTouchCancel">
        <view class="voice-button-svg"></view>
      </button>
    </block>
    <button class="upload-image-button" bind:tap="onTabImageUploader"
      wx:if="{{apiMode !== 3 && apiMode !== 4 && apiMode !== 5 && apiMode !== 6}}">+</button>
    <button class="send-button" bindtap="sendMessage" disabled="{{isStreaming || !inputMessage}}"
      wx:if="{{apiMode !== 4}}">发送</button>
    <!-- Voice Input Button with Long Press -->

    <view style="height: 130px;"></view>
  </view>

  <!-- Recording Indicator -->
  <view wx:if="{{isRecording}}" class="recording-indicator">
    <text>录音中...</text>
  </view>

  <canvas type="2d" id="canvas" style="position: fixed; top: 100000rpx;width: {{512}}px;height: {{512}}px;" />

</view>