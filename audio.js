class Audio {
    constructor() {
        this.audioList = []
        this.audioLength = 0
        this.playedCount = 0
        this.isPlaying = false
        this.endSend = false
        this.innerAudioContext = wx.createInnerAudioContext({
            useWebAudioImplement: false // 是否使用 WebAudio 作为底层音频驱动，默认关闭。对于短音频、播放频繁的音频建议开启此选项，开启后将获得更优的性能表现。由于开启此选项后也会带来一定的内存增长，因此对于长音频建议关闭此选项
        })
        wx.setInnerAudioOption({
            obeyMuteSwitch: false
        })
    }
    play() {
        if (this.isPlaying) return
        this.endSend = false
        this.playedCount = 0
        const audioContext = this.innerAudioContext
        audioContext.offEnded()
        const playAudio = () => {
            this.isPlaying = true
            this.playedCount++
            const audio = this.audioList.shift()
            audioContext.pause()
            audioContext.src = audio.filePath
            console.log('play', audio)
            audioContext.play();
        }
        if (!this.isPlaying && this.audioList.length > 0) {

            playAudio()
        }
        audioContext.onEnded(() => {
            console.log('play end')
            if (!this.isPlaying) return
            this.isPlaying = false
            if (this.audioList.length > 0) {
                playAudio()
            } else {
                if (this.playedCount === this.audioLength && this.endSend) {
                    this.stop()
                } else {
                    this.playWaitInterval = setInterval(() => {
                        if (this.audioList.length > 0) {
                            playAudio()
                            clearInterval(this.playWaitInterval)
                            this.playWaitInterval = null
                        }
                    }, 10);
                }
            }
        })
    }
    stop() {
        this.innerAudioContext.stop()
        this.isPlaying = false
        if (this.playWaitInterval) {
            clearInterval(this.playWaitInterval)
            this.playWaitInterval = null
        }
        this.audioList = []
    }
    onEnded(callback = res => {}) {
        callback({
            end: true
        })
    }
}

module.exports = Audio