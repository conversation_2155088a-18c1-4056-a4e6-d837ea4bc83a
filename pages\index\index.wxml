<view class="page">
  <status-bar isShowBack="{{false}}" title="主页"></status-bar>

  <view class="content">
    <navigator url="/pages/translator/translator">
      <view class="navi">翻译页面</view>
    </navigator>

    <navigator url="/pages/ASRServerTest/ASRServerTest">
      <view class="navi">平台ASR测试</view>
    </navigator>

    <view class="navi" bind:tap="startRecognizeThings">拍照识物</view>

    <view class="navi" bind:tap="startRecognizeSolve">拍照解题</view>

  </view>
</view>