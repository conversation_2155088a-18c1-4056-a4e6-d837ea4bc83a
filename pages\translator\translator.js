// pages/translator/translator.js
import {
  isDevTool,
  uint8ArrayToString,
  parseStreamData,
  arrayBufferToString,
  parseDataUrls,
  formatTimeFromTimestamp
} from '../../util'
import {
  kedaRange,
  weiruanRange,
  awsRange,
  aliRange
} from '../languages'
import Audio from '../../audio'
const fs = wx.getFileSystemManager()
const app = getApp()

const langMap = {
  keda: kedaRange,
  aws: awsRange,
  weiruan: weiruanRange
}

Page({
  data: {
    messages: [],
    inputMessage: '',
    isStreaming: false,
    scrollToView: '',
    userId: new Date().valueOf().toString(), // 示例用户ID
    isRecording: false, // 用于显示录音指示器,
    imageSrc: '',
    apiMode: 7,
    apiModeRange: [
      //     {
      //     id: 0,
      //     name: 'gpt-4o（不可用）'
      // }, {
      //     id: 1,
      //     name: '星火智能体-小蓝'
      // }, {
      //     id: 2,
      //     name: '智能体-老人陪聊'
      // }, {
      //     id: 3,
      //     name: '开关灯'
      // }, {
      //     id: 4,
      //     name: '翻译'
      // }, {
      //     id: 5,
      //     name: '涂鸦测试'
      // },
      //  {
      //     id: 6,
      //     name: '豆包'
      // }, 
      {
        id: 7,
        name: '翻译'
      },
      // {
      //     id: 8,
      //     name: '豆包翻译'
      // }, {
      //     id: 9,
      //     name: '谷歌翻译'
      // }
    ],
    lightList: [{
      name: '一楼客厅灯'
    }, {
      name: '厕所灯'
    }, {
      name: '二楼客厅空调'
    }, {
      name: '阳台灯'
    }, {
      name: '走廊灯'
    }],
    personList: [{
      name: '张三'
    }, {
      name: '李四'
    }, {
      name: '爸爸'
    }, {
      name: '妈妈'
    }, {
      name: '爷爷'
    }, {
      name: '奶奶'
    }, {
      name: '哥哥'
    }],

    canvasWidth: 200,
    canvasHeight: 300,

    isTranslating: false,
    translateText: '',
    translateUserText: '',
    translateUser1Messages: [

      // {
      //     user: 'system',
      //     message: '小蓝是超暖心的情感伙伴',
      //     isFinished: true,
      //     tragetAudio: '',
      //     imageUrls: ['https://yuuki.my/images/1743130189557-164290512.jpg']
      // }
    ],
    translateUser2Messages: [],
    languagesRange: kedaRange,
    user1Language: 'zh',
    user2Language: 'en',

    asrModels: [
      // {
      //   id: -1,
      //   name: '科大_小'
      // },
      {
        id: 0,
        name: '科大_大'
      }, {
        id: 1,
        name: '微软'
      }
      // , {
      //   id: 4,
      //   name: '科大_国外'
      // },
      // {
      //   id: 5,
      //   name: '豆包'
      // },
      , {
        id: 6,
        name: 'AWS'
      }

      // , {
      //     id: 2,
      //     name: '谷歌'
      // }
      , {
        id: 7,
        name: '阿里'
      }
    ],
    selectedAsrModel: 0,

    translateModels: [
      // {
      //     id: 0,
      //     name: '科大'
      // } ,
      // {
      //     id: 1,
      //     name: '豆包'
      // },
      {
        id: 2,
        name: 'deepseek'
      }
      // , {
      //     id: 3,
      //     name: 'gpt'
      // }
    ],
    ttsModels: [{
        id: 0,
        name: '科大'
      },
      // {
      //     id: 1,
      //     name: '豆包'
      // },
      {
        id: 2,
        name: '微软'
      }, {
        id: 3,
        name: '科大_国外'
      }, {
        id: 4,
        name: '阿里'
      }
    ],
    selectedTranslateModel: 2,
    selectedTtsModel: 0,

    isLoadingTranslate: false,
    isTts: false,

    asrServerConnected: false,
    otherSideIsConnected: false,

    systemMessageText: ''
  },

  onLoad() {
    this.Audio = new Audio()
  },

  onUnload() {
    if (this.data.isTranslating)
      this.switchGoogleTranslate()
  },

  onReady() {

    // if (!this.innerAudioContext) this.innerAudioContext = wx.createInnerAudioContext({
    //     useWebAudioImplement: false 
    // })

    // // 这一项必须要设置。不然ios手机很有可能播不出来声音。
    // wx.setInnerAudioOption({
    //     obeyMuteSwitch: false
    // })

    // this.innerAudioContext.onEnded(res => {
    //     this.isPlaying = false

    //     const url = this.audioList?.find(a => a.id === this.audioPlayId + 1)
    //     if (url) {
    //         this.innerAudioContext.src = url.url
    //         this.audioPlayId = url.id
    //         this.innerAudioContext.play()
    //         this.isPlaying = true
    //     }
    // })
  },

  onHide() {
    if (this.data.isRecording) {
      this.setData({
        isRecording: false
      })
    }
  },

  onInputChange(e) {
    this.setData({
      inputMessage: e.detail.value
    });
  },

  sendMessage() {
    if (this.data.inputMessage.trim() === '') return;

    const userMessage = {
      role: 'user',
      content: this.data.inputMessage
    };

    if (this.data.imageSrc) {
      userMessage.imageSrc = this.data.imageSrc
    }

    this.setData({
      messages: [...this.data.messages, userMessage],
      inputMessage: ''
    }, () => {
      this.scrollToBottom();

      if (this.data.imageSrc) {
        this.fetchChatWithImage(userMessage.content)
        this.clearImage()
      } else {
        if (this.data.apiMode === 3) this.fetchLight(userMessage.content)
        else if (this.data.apiMode === 5) this.fetchTuya(userMessage.content)
        else if (this.data.apiMode === 6) this.fetchDoubao(userMessage.content)
        else
          this.fetchChatResponse(userMessage.content);
      }

    });
  },

  startRecording(e) {
    const lang = e.currentTarget.dataset.lang
    this.lang = lang
    console.log(lang)

    // if (!this.innerAudioContext) this.innerAudioContext = wx.createInnerAudioContext({
    //     useWebAudioImplement: false 
    // })

    if (!this.recordManager) {
      this.recordManager = wx.getRecorderManager()
      this.recordManager.onStop(res => {
        if (this.data.apiMode === 6) return


        const recordingDuration = (Date.now() - this.recordingStartTime) / 1000;

        if (recordingDuration < 1) {
          return wx.showToast({
            title: '语音过短',
            icon: 'none'
          })
        }


        console.log('开始调接口', new Date())

        this.setData({
          isStreaming: true
        }, () => {
          this.scrollToBottom()
        })

        const filePath = res.tempFilePath

        if (this.data.apiMode === 4) {
          wx.uploadFile({
            url: 'https://yuuki.my/api/translate_audio', // 你的服务器地址
            filePath, // 文件路径
            name: 'audio', // form-data中字段的key
            formData: {
              lang: this.lang
            },
            timeout: 10000,
            success: res => {
              if (res.statusCode !== 200) {
                wx.showToast({
                  title: '服务器错误',
                  icon: 'error'
                })
                this.setData({
                  isStreaming: false
                })
                console.log(err);
              } else {
                const data = res.data;
                const text = JSON.parse(data).text
                const translation = JSON.parse(data).translation
                console.log(`text:${text} 。 translation: ${translation}`)
                const messages = [...this.data.messages]
                messages.push({
                  role: 'user',
                  content: text
                })
                const assistantMessage = {
                  role: 'assistant',
                  content: translation,
                  voiceUrl: ''
                }


                this.getTTSAudioStream(translation)
                  .then(res => {
                    assistantMessage.voiceUrl = res.voiceUrl
                  }).catch(err => {

                  }).finally(() => {
                    messages.push(assistantMessage)
                    this.setData({
                      messages,
                      isStreaming: false
                    }, () => {
                      this.scrollToBottom()
                      if (assistantMessage.voiceUrl) {
                        this.innerAudioContext.src = assistantMessage.voiceUrl
                        this.innerAudioContext.play()
                        this.isPlaying = true
                      }

                    })
                  })


              }

            },
            fail: err => {
              console.error('Upload failed', err);

              this.setData({
                isStreaming: false
              })

              wx.showToast({
                title: '错误',
                icon: 'error'
              })
            }
          })
          return
        }

        wx.uploadFile({
          url: 'https://yuuki.my/api/transcript', // 你的服务器地址
          filePath, // 文件路径
          name: 'audio', // form-data中字段的key
          formData: {
            userId: this.data.userId,
            apiMode: this.data.apiMode
          },
          timeout: 10000,
          success: res => {
            if (res.statusCode !== 200) {
              wx.showToast({
                title: '服务器错误',
                icon: 'error'
              })
              this.setData({
                isStreaming: false
              })
              console.log(err);
            } else {
              const data = res.data;
              const message = JSON.parse(data).transcript
              console.log('收到接口返回音频文字', message, new Date())
              this.setData({
                inputMessage: message
              }, () => {
                this.sendMessage()
              })
            }

          },
          fail: err => {
            console.error('Upload failed', err);

            this.setData({
              isStreaming: false
            })

            wx.showToast({
              title: '错误',
              icon: 'error'
            })
          }
        })
        return
        wx.uploadFile({
          url: 'https://yf.jfgou.com:9443/voice', // 你的服务器地址
          filePath, // 文件路径
          name: 'audio', // form-data中字段的key
          formData: {
            userId: this.data.userId,
            apiMode: this.data.apiMode
          },
          timeout: 20000,
          success: res => {
            const data = res.data;
            const message = JSON.parse(data).message
            const audioFileUrl = JSON.parse(data).audioFileUrl
            console.log('收到接口返回音频地址', new Date())
            // 处理服务器返回的数据
            const assistantMessage = {
              role: 'assistant',
              content: `语音消息：${message}`
            }
            this.setData({
              messages: [...this.data.messages, assistantMessage],
              isStreaming: false
            }, () => {
              this.scrollToBottom()
            })

            this.innerAudioContext.src = audioFileUrl
            this.innerAudioContext.play()
          },
          fail: err => {
            console.error('Upload failed', err);

            this.setData({
              isStreaming: false
            })

            wx.showToast({
              title: '错误',
              icon: 'error'
            })
          }
        })
      })
    }


    this.recordingStartTime = Date.now()
    this.recordManager.start({
      sampleRate: 8000,
      numberOfChannels: 1,
      format: 'mp3',
    })

    this.setData({
      isRecording: true,
    });
  },

  stopRecording(e) {
    this.recordManager.stop()

    const recordingDuration = (Date.now() - this.recordingStartTime) / 1000;
    if (recordingDuration < 1) {
      this.setData({
        isRecording: false,
      });
      return wx.showToast({
        title: '语音过短',
        icon: 'none'
      })
    }


    this.uploadVoice(recordingDuration);

    this.setData({
      isRecording: false,
    });
  },

  uploadVoice(duration) {
    // 示例代码，处理录音文件上传
    console.log('上传录音，时长: ', duration, '秒');

    this.innerAudioContext.stop()
    // 上传代码...
    // 例如：调用 /voice 接口，上传录音文件
    // 成功后在对话框中显示语音图标和时长
  },

  fetchChatResponse(message, imageUrl = '') {
    this.setData({
      isStreaming: true
    }, () => {
      this.scrollToBottom()
    });

    let assistantMessage = {
      role: 'assistant',
      content: ''
    };

    this.data.messages.push(assistantMessage);

    const requestTask = wx.request({
      url: 'https://yf.jfgou.com:9443/chat',
      method: 'POST',
      enableChunked: true,
      header: {
        'Content-Type': 'application/json'
      },
      enableQuic: true,
      enableCache: true,
      data: {
        userId: this.data.userId,
        message: message,
        apiMode: this.data.apiMode,
        imageUrl
      },
      timeout: 15000,
      success: res => {

        if (res.statusCode !== 200) {
          wx.showToast({
            title: '服务器错误',
            icon: 'error'
          })
          this.setData({
            isStreaming: false
          })
          console.log(err);
        }
      },
      fail: err => {
        this.setData({
          isStreaming: false
        })
        wx.showToast({
          title: '错误',
          icon: 'error'
        })
        console.log(err);
      }
    });

    this.audioList = []
    let isPlayedFirstAudio = false

    requestTask.onChunkReceived(res => {
      const data = res.data;
      let rawString = '';
      if (isDevTool) {
        rawString = uint8ArrayToString(data);
      } else {
        rawString = arrayBufferToString(data);
      }
      let parsedData = parseStreamData(rawString);
      console.log(this.handleFormatting(parsedData));

      let parsedUrl = parseDataUrls(rawString)
      if (parsedUrl.length > 0) {
        parsedUrl.forEach((u, idx) => {
          if (!isPlayedFirstAudio) {
            this.innerAudioContext.src = u
            this.audioPlayId = 0
            this.innerAudioContext.play()
            this.isPlaying = true
            isPlayedFirstAudio = true
            this.audioList.push({
              id: 0,
              url: u
            })
          } else {
            if (this.isPlaying === false) {
              this.innerAudioContext.src = u
              this.audioPlayId = this.audioList[this.audioList.length - 1].id + 1
              this.innerAudioContext.play()
              this.isPlaying = true
            }
            this.audioList.push({
              id: this.audioList[this.audioList.length - 1].id + 1,
              url: u
            })
          }

        })
      }

      if (parsedData.includes('data: [DONE]') || parsedData === '') {
        console.log('[DONE]')
        if (parsedData.split('data: [DONE]')[0].length > 0) {
          let d = this.handleFormatting(parsedData.split('data: [DONE]')[0])
          assistantMessage.content += d;
          this.updateMessages();
        }

        this.setData({
          isStreaming: false
        });
        return;
      }

      parsedData = this.handleFormatting(parsedData);

      assistantMessage.content += parsedData;
      this.updateMessages();
    });
  },

  handleFormatting(text) {
    let result = '';
    let buffer = '';
    const isChinese = /[\u4e00-\u9fa5]/;

    for (let char of text) {
      if (isChinese.test(char)) {
        if (buffer) {
          result += buffer;
          buffer = '';
        }
        result += char;
      } else {
        buffer += char;
        if ([' ', '.', '!', '?'].includes(char)) {
          result += buffer;
          buffer = '';
        }
      }
    }

    if (buffer) {
      result += buffer;
    }

    return result;
  },

  updateMessages() {
    this.setData({
      messages: this.data.messages
    }, () => {
      this.scrollToBottom();
    });
  },

  scrollToBottom() {
    this.setData({
      scrollToView: `bottom-anchor`
    });
  },

  onTapMessage(e) {
    const content = e.currentTarget.dataset.content

    const item = e.currentTarget.dataset.item

    if (this.Audio.isPlaying) {
      this.Audio.stop()
    } else {
      if (item.audioList?.length > 0) {
        this.Audio.audioList = item.audioList
        this.Audio.play()
      }

    }

    if (item.voiceUrl) {
      // this.innerAudioContext.src = item.voiceUrl
      // this.innerAudioContext.play()
      // this.isPlaying = true
    }

    if (content.includes('voiceMessage:')) {
      console.log('is voice')
      // this.innerAudioContext.play()
      // this.isPlaying = true
    }
  },

  onTabImageUploader() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      sizeType: ['compressed'],
      success: res => {
        console.log(res)
        const filePath = res.tempFiles[0].tempFilePath
        this.setData({
          imageSrc: filePath
        })
      }
    })
  },
  fetchChatWithImage(message) {
    if (this.data.apiMode !== 0) {
      this.setData({
        isStreaming: true
      }, () => {
        this.scrollToBottom()
      });
      wx.uploadFile({
        filePath: this.data.imageSrc,
        name: 'image',
        url: 'https://yf.jfgou.com:9443/upload_image',
        formData: {
          userId: this.data.userId,
          apiMode: this.data.apiMode,
        },
        success: res => {
          if (res.statusCode !== 200) {
            wx.showToast({
              title: '服务器错误',
              icon: 'error'
            })
            this.setData({
              isStreaming: false
            })
            console.log(err);
          } else {
            const data = res.data;
            const imageUrl = JSON.parse(data).imageUrl
            console.log(imageUrl)
            this.base64ToJpg(imageUrl)
              .then(res => {
                this.fetchChatResponse(message, res);
              })

          }

        },
        fail: err => {
          console.error('Upload failed', err);

          this.setData({
            isStreaming: false
          })
          wx.showToast({
            title: '错误',
            icon: 'error'
          })
        }
      })

      return
    }

    this.setData({
      isStreaming: true
    }, () => {
      this.scrollToBottom()
    });
    wx.uploadFile({
      filePath: this.data.imageSrc,
      name: 'image',
      url: 'https://yf.jfgou.com:9443/chat_with_image',
      formData: {
        userId: this.data.userId,
        apiMode: this.data.apiMode,
        message: message
      },
      timeout: 20000,
      success: res => {
        if (res.statusCode !== 200) {
          wx.showToast({
            title: '服务器错误',
            icon: 'error'
          })
          this.setData({
            isStreaming: false
          })
          console.log(res);
          return
        }
        const data = res.data;
        const message = JSON.parse(data).message
        const audioFileUrl = JSON.parse(data).audioFileUrl
        const assistantMessage = {
          role: 'assistant',
          content: `${message}`
        }
        this.setData({
          messages: [...this.data.messages, assistantMessage],
          isStreaming: false
        }, () => {
          this.scrollToBottom()
        })


        this.innerAudioContext.src = audioFileUrl
        this.innerAudioContext.play()
        this.isPlaying = true
      },
      fail: err => {
        console.error('Upload failed', err);

        this.setData({
          isStreaming: false
        })
        wx.showToast({
          title: '错误',
          icon: 'error'
        })
      }
    })
  },
  clearImage() {
    this.setData({
      imageSrc: ''
    })
  },
  onChangeApiMode(e) {
    const value = Math.round(e.detail.value)
    console.log(e)
    if (value === this.data.apiMode) return
    this.setData({
      apiMode: value,
      userId: new Date().valueOf().toString(),
      messages: []
    })
  },
  onTapAddLight() {
    wx.showModal({
      title: '请输入灯名字',
      content: '',
      placeholderText: '客厅',
      editable: true,
      complete: (res) => {
        if (res.cancel) {

        }

        if (res.confirm) {
          if (res.content) {
            const lightList = [...this.data.lightList]
            const isHave = lightList.find(i => i.name === res.content)
            if (isHave) return wx.showToast({
              title: '已存在该灯',
              icon: 'error'
            })
            lightList.push({
              name: res.content,
            })
            this.setData({
              lightList
            })
          }
        }
      }
    })
  },
  fetchLight(message) {
    this.setData({
      isStreaming: true
    })
    wx.request({
      url: 'https://yuuki.my/api/light',
      method: 'POST',
      data: {
        userId: this.data.userId,
        apiMode: 3,
        message,
        light_list: this.data.lightList.map(i => {
          return i.name
        })
      },
      success: res => {
        console.log(res.data)
        // 首先判断是否符合灯的指令
        try {
          const data = JSON.parse(res.data.data)
          const messages = [...this.data.messages]
          if (data.c === 1) {
            let switchTo = data.s
            if (switchTo === 2) switchTo = this.lastSwitch
            if (typeof switchTo !== 'number') switchTo = 1
            let switchText = switchTo === 0 ? '关掉' : '打开'
            messages.push({
              content: `好的，将为您${switchText}${data.m}的灯。（发送API到涂鸦，将 ${data.m}灯 给 ${switchText}）`,
              role: 'assistant'
            })
          } else if (data.c === 0) {
            messages.push({
              content: this.data.lightList.length === 0 ? '您还未添加任何灯' : `您的灯列表中没有该灯`,
              role: 'assistant'
            })
          } else {
            messages.push({
              content: `检测到多个灯：${data.m}。请问您要开哪站灯呢？`,
              role: 'assistant'
            })

            this.lastSwitch = data.s
          }
          this.setData({
            messages
          }, () => {
            this.scrollToBottom()
          })
        } catch (error) {
          const messages = [...this.data.messages]
          messages.push({
            content: '未检测到开关灯指令',
            role: 'assistant'
          })
          this.setData({
            messages
          }, () => {
            this.scrollToBottom()
          })
        }
        this.setData({
          isStreaming: false
        })
      },
      fail: err => {
        wx.showToast({
          title: '服务器错误',
          icon: 'error'
        })
        this.setData({
          isStreaming: false
        })
      }
    })
  },
  fetchTuya(message) {
    this.setData({
      isStreaming: true
    })
    wx.request({
      url: 'https://yf.jfgou.com:9443/tuya_control',
      method: 'POST',
      data: {
        message,
        device_id: 'vdevo172837680090938'
      },
      success: res => {
        console.log(res.data)
        const messages = [...this.data.messages]

        if (res.data.message) {
          messages.push({
            content: res.data.message ? res.data.message : '未检测到智能家居指令',
            role: 'assistant'
          })
          this.setData({
            isStreaming: false,
            messages
          })
        } else {
          this.fetchGpt(message)
        }

      },
      fail: err => {
        wx.showToast({
          title: '服务器错误',
          icon: 'error'
        })
        this.setData({
          isStreaming: false
        })
      }
    })
  },
  fetchGpt(message) {
    this.setData({
      isStreaming: true
    }, () => {
      this.scrollToBottom()
    });

    let assistantMessage = {
      role: 'assistant',
      content: ''
    };

    this.data.messages.push(assistantMessage);

    const requestTask = wx.request({
      url: 'https://yuuki.my/api/chat',
      method: 'POST',
      enableChunked: true,
      header: {
        'Content-Type': 'application/json'
      },
      enableQuic: true,
      enableCache: true,
      data: {
        user_id: this.data.userId,
        message: message,
      },
      timeout: 15000,
      success: res => {
        this.setData({
          isStreaming: false
        })
        if (res.statusCode !== 200) {
          wx.showToast({
            title: '服务器错误',
            icon: 'error'
          })
          this.setData({
            isStreaming: false
          })
        }
      },
      fail: err => {
        this.setData({
          isStreaming: false
        })
        wx.showToast({
          title: '错误',
          icon: 'error'
        })
        console.log(err);
      }
    });

    requestTask.onChunkReceived(res => {
      const data = res.data;
      let rawString = '';
      if (isDevTool) {
        rawString = uint8ArrayToString(data);
      } else {
        rawString = arrayBufferToString(data);
      }


      let parsedData = parseStreamData(rawString);
      console.log(parsedData)
      console.log(this.handleFormatting(parsedData));

      if (parsedData.includes('data: [DONE]') || parsedData === '' || parsedData === undefined) {
        console.log('[DONE]')
        if (parsedData.split('data: [DONE]')[0].length > 0) {
          let d = this.handleFormatting(parsedData.split('data: [DONE]')[0])
          assistantMessage.content += d;
          this.updateMessages();
        }

        this.setData({
          isStreaming: false
        });
        return;
      }

      parsedData = this.handleFormatting(parsedData);

      assistantMessage.content += parsedData;
      this.updateMessages();
    });

  },
  fetchDoubao(message) {
    this.setData({
      isStreaming: true
    }, () => {
      this.scrollToBottom()
    });
    this.Audio.stop()
    this.socketTask && this.socketTask.close()

    let assistantMessage = {
      role: 'assistant',
      content: '',
      audioList: []
    };

    this.data.messages.push(assistantMessage);

    this.socketTask = wx.connectSocket({
      url: 'wss://yuuki.my/ws2/',
      success: res => {
        console.log(res)
      }
    })
    this.socketTask.onOpen(() => {
      console.log('open')
      this.socketTask.send({
        data: JSON.stringify({
          personList: this.data.personList.map(i => i.name),
          lightList: this.data.lightList.map(i => i.name)
        })
      })
      this.socketTask.send({
        data: JSON.stringify({
          asrText: message
        })
      })
    })
    let index = 0
    let isPlay = false
    this.socketTask.onMessage(async message => {
      const response = JSON.parse(message.data);
      console.log(response)
      const {
        finish_reason,
        target_text,
        target_audio,
        image_url
      } = response
      if (finish_reason === 'STOP') {
        console.log(new Date().toISOString(), 'finish');
        this.Audio.endSend = true
        this.Audio.audioLength = index

        this.socketTask.close()
        this.setData({
          isStreaming: false
        }, () => {
          this.scrollToBottom()
        });
      } else {

        assistantMessage.content += target_text;
        // if (image_url) {
        //     const path = await this.base64ToJpg(image_url)
        //     assistantMessage.imageSrc = path
        // }
        if (image_url) assistantMessage.imageSrc = image_url


        const audio = target_audio
        if (!audio) return this.updateMessages();
        let filePath = ''
        if (audio.includes('http')) {
          filePath = target_audio
          this.Audio.audioList.push({
            filePath,
            index
          })
          assistantMessage.audioList.push({
            filePath,
            index
          })
          this.updateMessages()
          if (!isPlay) {
            this.Audio.play()
            isPlay = true
            console.log('call play')
          }
        } else {
          const audioArrayBuffer = wx.base64ToArrayBuffer(audio);
          index++
          filePath = `${wx.env.USER_DATA_PATH}/audio_${new Date().valueOf()}.mp3`; // 定义临时文件路径
          fs.writeFile({
            filePath: filePath,
            data: audioArrayBuffer,
            encoding: "binary",
            success: () => {
              console.log("Audio file saved successfully.");
              // 可以在这里调用播放函数

              this.Audio.audioList.push({
                filePath,
                index
              })
              assistantMessage.audioList.push({
                filePath,
                index
              })
              this.updateMessages()

              if (!isPlay) {
                this.Audio.play()
                isPlay = true
                console.log('call play')
              }
            },
            fail: (error) => {
              console.error("Failed to save audio file:", error);
            },
          });
        }
      }
    })



    return
    this.data.messages.push(assistantMessage);

    const requestTask = wx.request({
      url: 'https://yf.jfgou.com:9443/chat_doubao',
      method: 'POST',
      enableChunked: true,
      header: {
        'Content-Type': 'application/json'
      },
      enableQuic: true,
      enableCache: true,
      data: {
        message: message,
      },
      timeout: 15000,
      success: res => {
        this.setData({
          isStreaming: false
        })
        if (res.statusCode !== 200) {
          wx.showToast({
            title: '服务器错误',
            icon: 'error'
          })
          this.setData({
            isStreaming: false
          })
        }
      },
      fail: err => {
        this.setData({
          isStreaming: false
        })
        wx.showToast({
          title: '错误',
          icon: 'error'
        })
        console.log(err);
      }
    });

    if (!this.innerAudioContext) this.innerAudioContext = wx.createInnerAudioContext({
      useWebAudioImplement: false
    })

    wx.setInnerAudioOption({
      obeyMuteSwitch: false
    })

    this.audioQueue = []; // 用于存储下载的音频文件路径
    this.isPlaying = false; // 标记是否正在播放音频

    // 在收到消息块的处理代码中
    requestTask.onChunkReceived(res => {
      const data = res.data;
      let rawString = '';
      if (isDevTool) {
        rawString = uint8ArrayToString(data);
      } else {
        rawString = arrayBufferToString(data);
      }

      let parsedData = parseStreamData(rawString);
      const pData = JSON.parse(parsedData);
      const {
        target_text,
        finish_reason
      } = pData;

      if (finish_reason === 'STOP') {
        this.setData({
          isStreaming: false
        }, () => {
          this.scrollToBottom();
        });
        return;
      }

      console.log(target_text);

      // 下载音频文件并添加到播放队列
      wx.downloadFile({
        url: 'https://yf.jfgou.com:9443/tts_doubao?text=' + target_text,
        success: res => {
          const mp3File = res.tempFilePath;
          // 将下载的音频文件路径和顺序标识添加到队列中
          this.audioQueue.push({
            file: mp3File,
            order: this.audioQueue.length + 1 // 记录当前的顺序
          });

          // 如果当前没有播放音频，开始播放队列中的第一个文件
          if (!this.isPlaying) {
            this.playNextAudio();
          }
        }
      });

      assistantMessage.content += target_text;
      this.updateMessages();
    });

    // 定义播放下一个音频的函数
    this.playNextAudio = () => {
      if (this.audioQueue.length === 0) {
        this.isPlaying = false;
        return; // 队列为空，停止播放
      }

      // 按顺序播放
      const nextAudio = this.audioQueue.shift(); // 从队列中取出下一个音频文件
      this.isPlaying = true;
      this.innerAudioContext.src = nextAudio.file; // 使用路径播放音频
      this.innerAudioContext.play();

      // 显示当前正在播放的顺序
      console.log(`Playing audio ${nextAudio.order}: ${nextAudio.file}`);

      this.innerAudioContext.onEnded(() => {
        this.playNextAudio(); // 播放完当前音频后，播放下一个
      });

      this.innerAudioContext.onError((err) => {
        console.error('Audio playback failed:', err);
        this.playNextAudio(); // 跳过错误文件，继续播放下一个
      });
    };

  },
  onTouchCancel() {
    if (this.isRecording) {
      this.stopRecording()
    }

    return
  },
  async getTTSAudioStream(text) {
    return new Promise(async (resolve, reject) => {
      try {
        wx.request({
          url: `https://yuuki.my/api/tts?text=${text}`, // TTS 请求 URL
          responseType: 'arraybuffer', // 获取数据流
          success: (res) => {
            if (res.statusCode === 200) {
              // 将数据流保存为临时文件
              const filePath = wx.env.USER_DATA_PATH + `/audio_${new Date().valueOf()}.mp3`;

              wx.getFileSystemManager().writeFile({
                filePath,
                data: res.data,
                encoding: 'binary',
                success: () => {
                  resolve({
                    voiceUrl: filePath
                  })
                },
                fail(err) {
                  console.error('Failed to save audio file:', err);
                  reject({
                    errCode: -1,
                    errMsg: err
                  })
                }
              });
            } else {
              console.error('Failed to get TTS audio:', res);
              reject({
                errCode: -1,
                errMsg: res
              })
            }
          },
          fail(err) {
            console.error('Request failed:', err);
            reject({
              errCode: -1,
              errMsg: err
            })
          }
        });
      } catch (error) {
        reject({
          errCode: -1,
          errMsg: error
        })
      }
    })


  },
  onTapLightListItem(e) {
    const item = e.currentTarget.dataset.item
    let lightList = [...this.data.lightList]
    lightList = lightList.filter(i => i.name !== item.name)
    this.setData({
      lightList
    })
  },
  switchSpeaking() {
    console.log('switch')
    let assistantMessage = {
      role: 'assistant',
      content: '',
      audioList: []
    };

    let index = 0
    let isPushedUserMessage = false
    let isPushedAssistantMessage = false
    this.isRecordEnd = false

    this.setData({
      isRecording: !this.data.isRecording
    }, () => {
      if (this.data.isRecording) {
        this.lastSwitchSpeakingTime = new Date().valueOf()
        // 开始录音
        this.Audio.stop()
        this.socketTask && this.socketTask.close()
        this.socketTask = wx.connectSocket({
          url: 'ws://yf.jfgou.com:9444',
          success: res => {
            console.log(res)
          }
        })
        let isPlay = false
        this.socketTask.onMessage(async message => {
          const response = JSON.parse(message.data);
          console.log(response)
          const {
            finish_reason,
            target_text,
            target_audio,
            source_text,
            image_url
          } = response


          if (finish_reason === 'STOP') {
            console.log(new Date().toISOString(), 'finish');

            this.socketTask.close()
            this.setData({
              isStreaming: false
            }, () => {
              this.scrollToBottom()
            });
          } else {
            if (!source_text) {
              const userMessage = {
                role: 'user',
                content: '未检测到语音'
              };
              this.setData({
                messages: [...this.data.messages, userMessage],
                inputMessage: ''
              })
              this.setData({
                isStreaming: false
              }, () => {
                this.scrollToBottom()
              });
              this.socketTask.close();
              return
            }
            this.isRecordEnd = true
            this.recordManager.stop()
            this.setData({
              isStreaming: true,
              isRecording: false
            }, () => {
              this.scrollToBottom()
            })
            clearInterval(this.asrRecordingInterval)
            this.asrRecordingInterval = null

            if (!isPushedUserMessage) {
              isPushedUserMessage = true
              const userMessage = {
                role: 'user',
                content: source_text
              };
              this.setData({
                messages: [...this.data.messages, userMessage],
                inputMessage: ''
              })
            }

            if (!isPushedAssistantMessage) {
              isPushedAssistantMessage = true
              this.data.messages.push(assistantMessage);
            }
            if (!target_text) {
              return
            }

            assistantMessage.content += target_text;
            // if (image_url) {
            //     const path = await this.base64ToJpg(image_url)
            //     assistantMessage.imageSrc = path
            // }
            if (image_url) assistantMessage.imageSrc = image_url


            const audio = target_audio
            if (!audio) return this.updateMessages();
            let filePath = ''
            if (audio.includes('http')) {
              filePath = target_audio

              this.Audio.audioList.push({
                filePath,
                index
              })
              assistantMessage.audioList.push({
                filePath,
                index
              })
              this.updateMessages();
              if (!isPlay) {
                this.Audio.play()
                isPlay = true
                console.log('call play')
              }
            } else {
              const audioArrayBuffer = wx.base64ToArrayBuffer(audio);
              index++
              filePath = `${wx.env.USER_DATA_PATH}/audio_${new Date().valueOf()}.mp3`; // 定义临时文件路径
              fs.writeFile({
                filePath: filePath,
                data: audioArrayBuffer,
                encoding: "binary",
                success: () => {
                  console.log("Audio file saved successfully.");
                  // 可以在这里调用播放函数

                  this.Audio.audioList.push({
                    filePath,
                    index
                  })
                  assistantMessage.audioList.push({
                    filePath,
                    index
                  })
                  this.updateMessages()
                  if (!isPlay) {
                    this.Audio.play()
                    isPlay = true
                    console.log('call play')
                  }
                },
                fail: (error) => {
                  console.error("Failed to save audio file:", error);
                },
              });
            }
          }
        })
        this.socketTask.onOpen(() => {
          console.log('open')
          this.socketTask.send({
            data: JSON.stringify({
              personList: this.data.personList.map(i => i.name),
              lightList: this.data.lightList.map(i => i.name)
            })
          })
        })

        // 不间断地上传音频
        if (!this.recordManager) this.recordManager = wx.getRecorderManager()

        // this.recordManager.onStop(res => {
        //     const fs = wx.getFileSystemManager()
        //     const file = fs.readFileSync(res.tempFilePath)
        //     this.socketTask.send({
        //         data: file
        //     })
        // })
        this.recordManager.onFrameRecorded(res => {
          // if (this.isRecordEnd) return
          const file = res.frameBuffer
          this.socketTask.send({
            data: file
          })
          if (res.isLastFrame) {
            console.log('end')
            this.socketTask.send({
              data: JSON.stringify({
                end: true
              })
            });
          }
        })
        this.recordManager.start({
          sampleRate: 8000,
          numberOfChannels: 1,
          format: 'PCM',
          frameSize: 6
        })

        // if (this.asrRecordingInterval) clearInterval(this.asrRecordingInterval)
        // this.asrRecordingInterval = null
        // this.asrRecordingInterval = setInterval(() => {
        //     this.recordManager.stop()
        //     this.recordManager.start({
        //         sampleRate: 8000,
        //         numberOfChannels: 2,
        //         format: 'wav',
        //     })
        // }, 3000);

      } else {
        // 结束录音
        setTimeout(() => {
          this.recordManager.stop()
        }, 200);
        this.setData({
          isStreaming: true
        }, () => {
          this.scrollToBottom()
          if (new Date().valueOf() - this.lastSwitchSpeakingTime < 500) {
            this.setData({
              isStreaming: false
            })
            wx.showToast({
              title: '说话时间过短',
              icon: 'none'
            })
          }
        })

        clearInterval(this.asrRecordingInterval)
        this.asrRecordingInterval = null
      }
    })


  },
  onTapImage(e) {
    const src = e.currentTarget.dataset.src
    wx.previewImage({
      urls: [src],
      showmenu: true
    })
  },
  onTapAddPerson() {
    wx.showModal({
      title: '请输入名称',
      content: '',
      placeholderText: '叔叔',
      editable: true,
      complete: (res) => {
        if (res.cancel) {

        }

        if (res.confirm) {
          if (!res.content) res.content = '叔叔'
          const personList = [...this.data.personList]
          const isHave = personList.find(i => i.name === res.content)
          if (isHave) return wx.showToast({
            title: '已存在该名称',
            icon: 'error'
          })
          personList.push({
            name: res.content,
          })
          this.setData({
            personList
          })
        }
      }
    })
  },
  onTapPersonListItem(e) {
    const item = e.currentTarget.dataset.item
    let personList = [...this.data.personList]
    personList = personList.filter(i => i.name !== item.name)
    this.setData({
      personList
    })
  },
  async switchGoogleTranslate(e) {
    // const userType = e.currentTarget.dataset.type
    const userType = 0
    this.userType = userType
    this.lastTranslateUserText = this.data.translateUserText
    this.lastTranslateText = this.data.translateText
    let index = 0

    this.messages = [...this.data.translateUser1Messages]

    this.setData({
      isTranslating: !this.data.isTranslating,
      systemMessageText: ''
    }, () => {
      if (this.data.isTranslating) {
        // 开始翻译
        wx.showLoading({
          title: '连接中...',
          mask: true
        })
        let isFirstSend = true;
        this.setData({
          isLoadingTranslate: true
        })
        setTimeout(() => {
          wx.hideLoading()
          wx.showToast({
            title: '连接成功，开始录音...',
            icon: 'none',
            duration: 500
          })
          this.setData({
            isLoadingTranslate: false
          })
        }, 700);


        let timeoutFunction = () => {
          return
          this.socketTask.close()
          this.recordManager.stop()
          wx.showToast({
            title: '长时间无音频，自动停止录音',
            icon: 'none',
            duration: 1000
          })
          this.setData({
            isTranslating: false
          })
        }
        this.noTextTimeout && clearTimeout(this.noTextTimeout)
        // this.noTextTimeout = setTimeout(timeoutFunction, 25 * 1000);

        this.socketTask && this.socketTask.close()
        let baseUrl = 'ws://yf.jfgou.com:9444'
        if (this.data.selectedAsrModel == 2 || this.data.selectedTranslateModel == 3) baseUrl = 'ws://48.210.76.129:9444'

        baseUrl = 'wss://yuuki.my/ws/'
        // baseUrl = "wss://yf.jfgou.com:544/llm"
        // baseUrl = 'ws://192.168.102.100:9000'
        this.socketTask = wx.connectSocket({
          url: baseUrl,
          success: res => {}
        })


        const socketTask = this.socketTask

        this.socketTask.onOpen(() => {
          this.setData({
            asrServerConnected: true
          })

          if (!this.recordManager) this.recordManager = wx.getRecorderManager()
          this.socketTask.send({
            data: JSON.stringify({
              asr_language: this.userType === 0 ? this.data.user1Language : this.data.user2Language,
              translate_language: this.userType === 0 ? this.data.user2Language : this.data.user1Language,
              asr_model: this.data.selectedAsrModel,
              translate_model: this.data.selectedTranslateModel,
              tts: this.data.isTts,
              tts_model: this.data.selectedTtsModel,
              sessionId: this.data.apiMode === 10 ? '123123' : '-1'
            }),
          });

          const startRecord = () => {
            this.recordManager.start({
              sampleRate: 16000,
              numberOfChannels: 1,
              format: 'PCM',
              frameSize: 1
            })
          }

          this.recordManager.onFrameRecorded(res => {
            const file = res.frameBuffer
            this.socketTask.send({
              data: file
            })
            console.log('onFrameRecorded')
            if (res.isLastFrame) {
              console.log('end')

              if (this.data.isTranslating) { // 系统导致的录音自动结束
                setTimeout(() => {
                  startRecord()
                }, 10);
              } else { // 手动结束录音时
                this.socketTask.send({
                  data: JSON.stringify({
                    end: true
                  }),
                });
              }
            }
          })

          startRecord()
        })
        this.socketTask.onMessage(async message => {
          clearTimeout(this.noTextTimeout)
          this.noTextTimeout = setTimeout(() => {
            timeoutFunction()
          }, 10 * 1000);

          let isPlay = false
          const response = JSON.parse(message.data);
          console.log(response)
          if (response.type === 'asr') {
            if (response?.language) {
              if (userType === 0) {
                this.setData({
                  user1Language: response.language
                })
              } else {
                this.setData({
                  user2Language: response.language
                })
              }
            }

            if (response.isFinal) {
              this.messages[0].isFinished = true
              this.messages[0].message = response.transcript
              this.setData({
                translateUser1Messages: this.messages
              })
              // if (this.userType === 0) {
              //     const messages = [...this.data.translateUser1Messages]
              //     messages[0].isFinished = true
              //     messages[0].message = response.transcript
              //     this.setData({
              //         translateUser1Messages: messages
              //     })
              // } else {
              //     const messages = [...this.data.translateUser2Messages]
              //     messages[0].isFinished = true
              //     messages[0].message = response.transcript
              //     this.setData({
              //         translateUser2Messages: messages
              //     })
              // }
              // const text = `\n${response.transcript}${this.lastTranslateUserText}`
              // this.setData({
              //     translateUserText: text
              // })
              // this.lastTranslateUserText = text
            } else {
              if (this.messages?.[0]?.isFinished) {
                this.messages.unshift({
                  message: response.transcript,
                  user: 'user1',
                  isFinished: false,
                })
              } else {
                this.messages[0] = {
                  message: response.transcript,
                  user: 'user1',
                  isFinished: false,
                }
              }
              this.setData({
                translateUser1Messages: this.messages
              })
              // if (this.userType === 0) {
              //     const messages = [...this.data.translateUser1Messages]
              //     if (messages?.[0]?.isFinished) {
              //         messages.unshift({
              //             message: response.transcript,
              //             user: 'user1',
              //             isFinished: false
              //         })
              //     } else {
              //         messages[0] = {
              //             message: response.transcript,
              //             user: 'user1',
              //             isFinished: false
              //         }
              //     }
              //     this.setData({
              //         translateUser1Messages: messages
              //     })
              // } else {
              //     const messages = [...this.data.translateUser2Messages]
              //     if (messages?.[0]?.isFinished) {
              //         messages.unshift({
              //             message: response.transcript,
              //             user: 'user2',
              //             isFinished: false
              //         })
              //     } else {
              //         messages[0] = {
              //             message: response.transcript,
              //             user: 'user2',
              //             isFinished: false
              //         }
              //     }
              //     this.setData({
              //         translateUser2Messages: messages
              //     })
              // }

              // this.setData({
              //     translateUserText: `${response.transcript}${this.lastTranslateUserText}`
              // })
            }
          } else if (response.type === 'translate') {
            const {
              finish_reason,
              target_text,
              target_audio
            } = response
            if (finish_reason === 'start') {
              if (this.userType === 1) {
                const messages = [...this.data.translateUser2Messages]
                messages.unshift({
                  message: target_text,
                  user: 'user1',
                  isFinished: false,
                  targetAudio: target_audio
                })
                this.setData({
                  translateUser2Messages: messages
                })
              } else {
                this.messages.unshift({
                  message: target_text,
                  user: 'user2',
                  isFinished: false,
                  targetAudio: target_audio
                })
                this.setData({
                  translateUser1Messages: this.messages
                })
              }
            } else if (finish_reason === 'stop') {
              if (this.userType === 1) {
                const messages = [...this.data.translateUser2Messages]
                messages[0].message = target_text
                messages[0].isFinished = true
                messages[0].targetAudio = target_audio
                this.setData({
                  translateUser2Messages: messages
                })
              } else {
                this.messages[0].message = target_text
                this.messages[0].isFinished = true
                this.messages[0].targetAudio = target_audio
                this.setData({
                  translateUser1Messages: this.messages
                })
              }

              if (target_audio) {
                const audioArrayBuffer = wx.base64ToArrayBuffer(target_audio);
                index++
                let filePath = `${wx.env.USER_DATA_PATH}/audio_${new Date().valueOf()}.mp3`; // 定义临时文件路径
                fs.writeFile({
                  filePath: filePath,
                  data: audioArrayBuffer,
                  encoding: "binary",
                  success: () => {
                    console.log("Audio file saved successfully.");
                    // 可以在这里调用播放函数

                    this.Audio.audioList.push({
                      filePath,
                      index
                    })
                    if (!isPlay) {
                      this.Audio.play()
                      console.log(this.Audio)
                      isPlay = true
                      console.log('call play')
                    }
                  },
                  fail: (error) => {
                    console.error("Failed to save audio file:", error);
                  },
                });

              }
            } else {
              if (this.userType === 1) {
                const messages = [...this.data.translateUser2Messages]
                messages[0] = {
                  message: target_text,
                  user: 'user1',
                  isFinished: false,
                  targetAudio: target_audio
                }
                this.setData({
                  translateUser2Messages: messages
                })
              } else {
                const firstTranslate = this.messages.find(i => i.isFinished !== undefined)
                if (!firstTranslate) return
                firstTranslate.message = target_text
                firstTranslate.targetAudio = target_audio
                firstTranslate.isFinished = false

                this.setData({
                  translateUser1Messages: this.messages
                })
              }
            }
          } else if (response.type === 'system') {
            const {
              content,
              imageUrls,
              isFinished,
              lastSystemTime,
              isStart
            } = response

            imageUrls.forEach(i => {
              i = i.replace('localhost', 'yuuki.my')
            })
            if (isStart) {
              console.log('system start')
              this.messages.unshift({
                message: '',
                user: 'system',
                isFinished: false,
                targetAudio: '',
                imageUrls: [],
                systemTime: lastSystemTime
              })
            } else {
              const sysMessage = this.messages.find(i => i.systemTime === lastSystemTime)
              if (!sysMessage) return
              sysMessage.isFinished = isFinished
              sysMessage.imageUrls = imageUrls
              sysMessage.message = content
            }

            this.setData({
              translateUser1Messages: this.messages
            }, () => {})
          } else if (response.type === 'connect') {
            const {
              isBothConnected
            } = response
            this.setData({
              otherSideIsConnected: isBothConnected
            })
          } else if (response.type === 'message') {
            this.setData({
              systemMessageText: response.content
            })
          }
        })
        this.socketTask.onClose(() => {
          this.setData({
            asrServerConnected: false,
            otherSideIsConnected: false
          })
        })
        return
        // 测试服务器发送
        this.socketTask.onOpen(() => {
          console.log('open')
          if (!this.recordManager) this.recordManager = wx.getRecorderManager()
          this.recordManager.onFrameRecorded(res => {
            const file = res.frameBuffer
            const base64 = wx.arrayBufferToBase64(res.frameBuffer)
            const requestObj = {
              reqId: "dev_abcd5341_1",
              status: isFirstSend ? 0 : 1,
              audio: base64,
              lang: 'zh',
              transLang: 'en',
              opcode: 'asr2trans'
            }
            isFirstSend = false
            socketTask.send({
              data: JSON.stringify(requestObj)
            })
            console.log('send')
            if (res.isLastFrame) {
              console.log('end')

              const requestObj = {
                reqId: "dev_abcd2341_1",
                status: 2,
                audio: base64,
                lang: 'zh',
                transLang: 'en'
              }
              socketTask.send({
                data: JSON.stringify(requestObj)
              })


              if (this.data.isTranslating) {
                this.switchGoogleTranslate()
                setTimeout(() => {
                  this.switchGoogleTranslate()
                }, 6);
              }
            }
          })
          this.recordManager.start({
            sampleRate: 16000,
            numberOfChannels: 1,
            format: 'PCM',
            frameSize: 2
          })
        })
        this.socketTask.onMessage(async message => {
          clearTimeout(this.noTextTimeout)
          const response = JSON.parse(message.data);
          console.log(response)

          if (response.asr) {
            this.messages.unshift({
              message: response.asr,
              user: 'user1',
              isFinished: true,
            })
            this.setData({
              translateUser1Messages: this.messages
            })
          }

          if (response.trans) {
            const messages = [...this.data.translateUser2Messages]
            messages.unshift({
              message: response.trans,
              user: 'user1',
              isFinished: false,
              targetAudio: ''
            })
            this.setData({
              translateUser2Messages: messages
            })
          }

        })
        this.socketTask.onClose(() => {
          this.setData({
            asrServerConnected: false,
            otherSideIsConnected: false
          })
        })
      } else {
        // 结束翻译
        this.recordManager.stop()
        wx.showToast({
          title: '已停止录音',
          icon: 'none',
          duration: 500
        })
        clearTimeout(this.noTextTimeout)
        // this.socketTask.close()
      }
    })
  },
  // 停止翻译
  async stopGoogleTranslate() {
    this.setData({
      isTranslating: false
    })
    this.recordManager.stop()
    this.socketTask.close()
  },
  onTapSystemImage(e) {
    const src = e.currentTarget.dataset.src
    wx.previewImage({
      urls: [src],
    })
  },
  switchUser1Language(e) {
    const lang = e.currentTarget.dataset.lang
    if (lang === this.data.user1Language) return
    this.setData({
      user1Language: lang
    })
    if (this.data.isTranslating)
      this.switchGoogleTranslate()
  },
  switchUser2Language(e) {
    const lang = e.currentTarget.dataset.lang
    if (lang === this.data.user2Language) return
    this.setData({
      user2Language: lang
    })
    if (this.data.isTranslating)
      this.switchGoogleTranslate()

  },
  switchTranslateModel(e) {
    const id = e.currentTarget.dataset.id
    if (id === this.data.selectedTranslateModel) {

    } else {
      this.setData({
        selectedTranslateModel: id
      })
      if (this.data.isTranslating)
        this.switchGoogleTranslate()
    }
  },
  switchTtsModel(e) {
    const id = e.currentTarget.dataset.id
    if (id === this.data.selectedTtsModel) {

    } else {
      this.setData({
        selectedTtsModel: id
      })
      if (this.data.isTranslating)
        this.switchGoogleTranslate()
    }
  },
  switchAsrModel(e) {
    const id = e.currentTarget.dataset.id
    if (id === this.data.selectedAsrModel) {

    } else {
      let asrRange = kedaRange
      if (id == 1) {
        asrRange = weiruanRange
      } else if (id == 6) {
        asrRange = awsRange
      } else if (id === 7) {
        asrRange = aliRange
      } else if (id == 4) {
        wx.showToast({
          title: '科大国外暂无额度',
          icon: 'error'
        })
        return
      }

      this.setData({
        selectedAsrModel: id,
        languagesRange: asrRange
      })

      if (this.data.isTranslating)
        this.switchGoogleTranslate()
    }
  },
  onShareAppMessage() {
    return {
      title: '我的小程序日志',
      // 这里分享的是小程序页面，好友点击后会进到这个页面
      // 具体日志内容已在剪贴板，可以粘贴查看
      path: '/pages/your-log-page/your-log-page'
    };
  },
  exportRecord() {
    wx.showLoading({
      title: '导出中...',
      mask: true
    });
    // 1. 用内存里的数组直接拼出文本
    const list = [...this.data.translateUser1Messages];
    const lines = list.reverse().map(item =>
      item.user === 'user1' ? `ASR：${item.message}` : `翻译：${item.message}`
    );
    const content = `log time: ${formatTimeFromTimestamp(Date.now(), 1)}\n\n` + lines.join('\n');

    // 2. 复制到剪贴板
    wx.setClipboardData({
      data: content,
      success() {
        wx.hideLoading();
        // 3. 弹一个提示/对话框，让用户知道已经复制完毕，并引导点击“分享”按钮
        wx.showModal({
          title: '日志已复制',
          content: '日志内容已复制到剪贴板',
          showCancel: false,
          confirmText: '好的'
        });
      },
      fail() {
        wx.hideLoading();
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // exportRecord() {
  //   wx.showLoading({
  //     title: '导出中...',
  //     mask: true
  //   })
  //   const LOG_BASE_PATH = `${wx.env.USER_DATA_PATH}/log.txt`
  //   let isLogExist = false
  //   try {
  //     fs.accessSync(LOG_BASE_PATH)
  //     console.log('初始化日志系统。日志文件存在，清除残留日志')
  //     isLogExist = true
  //     fs.writeFileSync(LOG_BASE_PATH, '', 'utf8');
  //   } catch (e) {
  //     console.log('初始化日志系统。日志文件不存在，创建日志')
  //   }
  //   const asrLanguage = this.data.languagesRange.find(i => i.lang === this.data.user1Language)?.langText
  //   const translateLanguage = this.data.languagesRange.find(i => i.lang === this.data.user2Language)?.langText

  //   let isCreateError = false
  //   if (!isLogExist) {
  //     try {
  //       fs.writeFileSync(LOG_BASE_PATH, `log time: ${formatTimeFromTimestamp(new Date().valueOf(), 1)}\n\n\n\n`)
  //       console.log('创建日志成功')
  //     } catch (e) {
  //       console.log('创建日志文件失败', e)
  //       isCreateError = true
  //     }
  //   }

  //   const writeLog = (data) => {
  //     fs.appendFileSync(LOG_BASE_PATH, `\n${data}`, 'utf-8')
  //   }

  //   if (isCreateError) {
  //     wx.hideLoading()
  //     return wx.showToast({
  //       title: 'write log error',
  //       icon: 'none'
  //     })
  //   }
  //   const list = [...this.data.translateUser1Messages]
  //   for (let i = 0; i < list.length; i++) {
  //     let text = ''
  //     let index = (list.length - 1) - i
  //     const item = list[index]
  //     if (item.user == 'user1') {
  //       text = `ASR：${item.message}`
  //     } else {
  //       text = `Translation：${item.message}`
  //     }
  //     writeLog(text)
  //   }

  //   const path1 = `${wx.env.USER_DATA_PATH}/${formatTimeFromTimestamp(new Date().valueOf(), 1).replace(/[-: ]/,'_')}__translate.txt`
  //   fs.saveFile({
  //     tempFilePath: LOG_BASE_PATH,
  //     filePath: path1,
  //     success: res => {
  //       const path = res.savedFilePath
  //       wx.openDocument({
  //         filePath: path,
  //         showMenu: true
  //       })
  //     },
  //     complete: () => {
  //       wx.hideLoading()
  //     }
  //   })

  // },
  //test
  test() {
    console.log(this.getTTSAudioStream('你好你是谁'))
  },
  async base64ToJpg(base64Data) {
    return new Promise((resolve, reject) => {
      this.createSelectorQuery().select('#canvas').fields({
        node: true,
        size: true
      }).exec(res => {
        const canvas = res[0].node
        canvas.width = 512
        canvas.height = 512
        const ctx = canvas.getContext('2d')
        const image = canvas.createImage()
        image.src = base64Data;

        image.onload = () => {
          ctx.drawImage(image, 0, 0, 512, 512)
          wx.canvasToTempFilePath({
            canvas,
            fileType: 'jpg',
            success: tempRes => {
              const imageTempFilePath = tempRes.tempFilePath
              resolve(imageTempFilePath)
            }
          })
        }

      })

      return



    });
  },
  onTtsEnableChange(e) {
    const value = e.detail.value
    this.setData({
      isTts: value
    }, () => {
      if (this.data.isTranslating)
        this.switchGoogleTranslate()
    })
  },
  getPreTime() {
    return `${formatTimeFromTimestamp(new Date().valueOf(), 1)}:  `
  },
  onTapContent(e) {
    const item = e.currentTarget.dataset.item
    console.log(item)

    if (item.targetAudio) {
      const audioArrayBuffer = wx.base64ToArrayBuffer(item.targetAudio);
      let filePath = `${wx.env.USER_DATA_PATH}/audio_${new Date().valueOf()}.mp3`; // 定义临时文件路径
      fs.writeFile({
        filePath: filePath,
        data: audioArrayBuffer,
        encoding: "binary",
        success: () => {
          console.log("Audio file saved successfully.");
          // 可以在这里调用播放函数
          this.Audio.stop()
          this.Audio.audioList.push({
            filePath,
            index: 0
          })
          this.Audio.play()
          console.log(this.Audio)
          isPlay = true
          console.log('call play')
        },
        fail: (error) => {
          console.error("Failed to save audio file:", error);
        },
      });
    }
  },
  switchTranslateSpeakMode() {
    this.setData({
      apiMode: this.data.apiMode === 7 ? 10 : 7
    })
    if (this.data.isTranslating)
      this.switchGoogleTranslate()
  },
  clearMessages() {
    this.setData({
      translateUser1Messages: [],
      translateUser2Messages: []
    })
    this.messages = []
  }
});