{"name": "towxml", "version": "3.0.6", "description": "HTML、Markdown转WXML(WeiXin Markup Language)渲染库", "main": "index.js", "scripts": {"build": "node build", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/sbfkcel/towxml.git"}, "keywords": ["html to wxml", "markdown to wxml", "html2wxml", "mark2wxml", "wxml"], "author": "<EMAIL>", "license": "MIT", "bugs": {"url": "https://github.com/sbfkcel/towxml/issues"}, "homepage": "https://github.com/sbfkcel/towxml#readme", "dependencies": {"fs-extra": "^8.1.0"}}