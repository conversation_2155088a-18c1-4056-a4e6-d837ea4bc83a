// pages/imageChater/imageChater.js
import Audio from '../../audio'
import {
  sampleBase64
} from '../../utils/imageBase64Sample'
const fs = wx.getFileSystemManager()
const app = getApp()
Page({

  data: {
    imageUrl: '',
    titleText: '',
    isStreaming: false,
    recogType: 0
  },


  onLoad(options) {
    this.Audio = new Audio()

    if (options.imageUrl) {
      this.setData({
        imageUrl: options.imageUrl
      })
    }
  },

  onReady() {
    this.recognizeImage()
  },

  onShow() {

  },

  onHide() {

  },

  onUnload() {
    this.Audio.stop()
  },

  onTapTest(e) {
    this.startRegconize();
  },

  recognizeImage() {
    if (this.data.imageUrl) {
      wx.getFileSystemManager().readFile({
        filePath: this.data.imageUrl, //要读取的文件的路径 (本地路径)
        encoding: "base64", //指定读取文件的字符编码，如果不传 encoding，则以 ArrayBuffer 格式读取文件的二进制内容
        success: res => {
          this.isAlreadyInit = false

          const base64Data = 'data:image/jpg;base64,' + res.data
          this.setData({
            imageUrl: base64Data
          })

          this.startChat(`
          你现在是一个儿童拍照玩具的拍照识别助手。你需要识别出图片中有什么东西。
          ## 要求：你只能输出普通文本格式，不要输出Markdown格式，因为文本需要被展示在普通显示屏上。回复要以"这是"开头。
        `, ['data:image/jpg;base64,' + res.data])
        }
      })
    }
  },

  startChat(text = "", imageUrls = []) {
    this.Audio.stop()
    this.socketTask = wx.connectSocket({
      // url: 'ws://yf.jfgou.com:9444',
      url: 'wss://yuuki.my/ws2/',
      success: res => {
        console.log(res)
      }
    })

    let index = 0
    let isPlay = false

    this.socketTask.onOpen(() => {
      this.socketTask.send({
        data: JSON.stringify({
          asrText: text,
          imageUrls: imageUrls
        })
      })
      this.setData({
        isStreaming: true,
        titleText: ''
      })

    })

    this.socketTask.onMessage(async message => {
      const response = JSON.parse(message.data);
      console.log(response)
      const assistantMessage = {
        role: 'assistant',
        content: this.data.titleText,
        voiceUrl: '',
        audioList: []
      }
      const {
        finish_reason,
        target_text,
        target_audio,
        image_url
      } = response
      if (finish_reason === 'STOP') {
        console.log(new Date().toISOString(), 'finish');
        this.Audio.endSend = true
        this.Audio.audioLength = index

        if (!this.isAlreadyInit) {
          this.isAlreadyInit = true
          this.recognizedText = this.data.titleText
        }

        this.socketTask.close()
        this.setData({
          isStreaming: false
        }, () => {});
      } else {
        assistantMessage.content += target_text;
        this.setData({
          titleText: assistantMessage.content
        })
        // if (image_url) {
        //     const path = await this.base64ToJpg(image_url)
        //     assistantMessage.imageSrc = path
        // }
        if (image_url) assistantMessage.imageSrc = image_url


        const audio = target_audio
        if (!audio) return;
        let filePath = ''
        if (audio.includes('http')) {
          filePath = target_audio
          this.Audio.audioList.push({
            filePath,
            index
          })
          assistantMessage.audioList.push({
            filePath,
            index
          })
          if (!isPlay) {
            this.Audio.play()
            isPlay = true
            console.log('call play')
          }
        } else {
          const audioArrayBuffer = wx.base64ToArrayBuffer(audio);
          index++
          filePath = `${wx.env.USER_DATA_PATH}/audio_${new Date().valueOf()}.mp3`; // 定义临时文件路径
          fs.writeFile({
            filePath: filePath,
            data: audioArrayBuffer,
            encoding: "binary",
            success: () => {
              console.log("Audio file saved successfully.");

              this.Audio.audioList.push({
                filePath,
                index
              })
              assistantMessage.audioList.push({
                filePath,
                index
              })

              if (!isPlay) {
                this.Audio.play()
                isPlay = true
                console.log('call play')
              }
            },
            fail: (error) => {
              console.error("Failed to save audio file:", error);
            },
          });
        }
      }

    })
  },

  shot() {
    this.setData({
      recogType: 0
    })
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sizeType: 'compressed',
      success: res => {
        let temp = res.tempFiles[0].tempFilePath
        wx.cropImage({
          src: temp,
          cropScale: "1:1",
          success: async r => {
            let tempCrop = r.tempFilePath

            this.setData({
              imageUrl: tempCrop
            }, () => {
              this.recognizeImage()
            })
          },
          fail: err => {
            console.log(err)
            wx.showToast({
              title: '拍照失败',
              icon: 'error'
            })
          }
        })
      },
      fail: err => {
        console.log(err)
      }
    })
  },

  baike() {
    if (this.recognizedText) {
      this.setData({
        recogType: 1
      })
      this.startChat(`你是一个儿童拍照玩具。请根据图片识别的内容来进行一个百科回答，为小朋友进行科普。
      #要求：你不能回复markdown格式的内容
      以下是图片识别的内容：${ this.recognizedText}`, [])
    }
  },

  story() {
    if (this.recognizedText) {
      this.setData({
        recogType: 2
      })
      this.startChat(`你是一个儿童拍照玩具。请根据图片识别的内容来生成一个故事，为小朋友进行讲故事。
      #要求：你不能回复markdown格式的内容。
      以下是图片识别的内容：${ this.recognizedText}`, [])
    }
  },

})